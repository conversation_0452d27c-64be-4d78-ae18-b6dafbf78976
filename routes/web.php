<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Auth\Login;

// Home
use App\Livewire\Pages\Home\Index as HomeIndex;

// Users
use App\Livewire\Pages\Users\Index as UsersIndex;
use App\Livewire\Pages\Users\Show as UsersShow;
use App\Livewire\Pages\Users\Create as UsersCreate;
use App\Livewire\Pages\Users\Edit as UsersEdit;

// Roles
use App\Livewire\Pages\Roles\Index as RolesIndex;
use App\Livewire\Pages\Roles\Create as RolesCreate;
use App\Livewire\Pages\Roles\Show as RoleShow;
use App\Livewire\Pages\Roles\Edit as RoleEdit;


// Contacts
use App\Livewire\Pages\Contacts\Index as ContactsIndex;
use App\Livewire\Pages\Contacts\Create as ContactsCreate;
use App\Livewire\Pages\Contacts\Show as ContactsShow;
use App\Livewire\Pages\Contacts\Edit as ContactsEdit;

// Clients
use App\Livewire\Pages\Clients\Index as ClientsIndex;
use App\Livewire\Pages\Clients\Create as ClientsCreate;
use App\Livewire\Pages\Clients\Show as ClientsShow;
use App\Livewire\Pages\Clients\Edit as ClientsEdit;

// Partners
use App\Livewire\Pages\Partners\Index as PartnersIndex;
use App\Livewire\Pages\Partners\Create as PartnersCreate;
use App\Livewire\Pages\Partners\Show as PartnersShow;
use App\Livewire\Pages\Partners\Edit as PartnersEdit;

// Products
use App\Livewire\Pages\Products\Index as ProductsIndex;

// Orders
use App\Livewire\Pages\Orders\Index as OrdersIndex;
use App\Livewire\Pages\Orders\Create as OrdersCreate;
use App\Livewire\Pages\Orders\Show as OrdersShow;
use App\Livewire\Pages\Orders\Edit as OrdersEdit;
// Order - Creator
use App\Livewire\Pages\Orders\Creator\Index as CreatorIndex;
// Order - Stats
use App\Livewire\Pages\Orders\Stats\Index as StatsIndex;

// Projects
use App\Livewire\Pages\Projects\Index as ProjectsIndex;
use App\Livewire\Pages\Projects\Create as ProjectsCreate;
use App\Livewire\Pages\Projects\Show as ProjectsShow;
use App\Livewire\Pages\Projects\Edit as ProjectsEdit;

// Collections
use App\Livewire\Pages\Collections\Index as CollectionIndex;
use App\Livewire\Pages\Collections\Show as CollectionShow;
use App\Livewire\Pages\Collections\Create as CollectionCreate;
use App\Livewire\Pages\Collections\Edit as CollectionEdit;

// Brands
use App\Livewire\Pages\Brands\Index as BrandsIndex;
use App\Livewire\Pages\Brands\Create as BrandsCreate;
use App\Livewire\Pages\Brands\Show as BrandsShow;
use App\Livewire\Pages\Brands\Edit as BrandsEdit;

// Suppliers
use App\Livewire\Pages\Suppliers\Index as SuppliersIndex;
use App\Livewire\Pages\Suppliers\Create as SuppliersCreate;
use App\Livewire\Pages\Suppliers\Show as SuppliersShow;
use App\Livewire\Pages\Suppliers\Edit as SuppliersEdit;

// Discount Groups
use App\Livewire\Pages\DiscountGroups\Index as DiscountGroupIndex;
use App\Livewire\Pages\DiscountGroups\Create as DiscountGroupCreate;
use App\Livewire\Pages\DiscountGroups\Show as DiscountGroupShow;
use App\Livewire\Pages\DiscountGroups\Edit as DiscountGroupEdit;

// Imports
use App\Livewire\Pages\Imports\Index as ImportIndex;
use App\Livewire\Pages\Imports\Create as ImportCreate;

// Exports
use App\Livewire\Pages\Exports\Index as ExportIndex;
use App\Livewire\Pages\Exports\Create as ExportCreate;

// DEMO - Livewire Components
use App\Livewire\Demo\BaseOrder;
use App\Livewire\Demo\DuplicatedOrder;
use App\Livewire\Demo\ReOrder;
use App\Livewire\Demo\SingleClient;

Route::get('/login', Login::class)->middleware('guest')->name('login');

Route::group(['middleware' => ['auth']], function () {

    /*
     * Pages - Index
     */
    Route::get('/', HomeIndex::class)->name('home');
    Route::get('/users', UsersIndex::class)->name('users.index')
        ->middleware('can:viewAny,App\Models\User');
    Route::get('/contacts', ContactsIndex::class)->name('contacts.index')
        ->middleware('can:viewAny,App\Models\Contact');
    Route::get('/clients', ClientsIndex::class)->name('clients.index')
        ->middleware('can:viewAny,App\Models\Client');
    Route::get('/partners', PartnersIndex::class)->name('partners.index')
        ->middleware('can:viewAny,App\Models\Partner');
    Route::get('/suppliers', SuppliersIndex::class)->name('suppliers.index')
        ->middleware('can:viewAny,App\Models\Supplier');
    Route::get('/brands', BrandsIndex::class)->name('brands.index')
        ->middleware('can:viewAny,App\Models\Brand');
    Route::get('/discount-groups', DiscountGroupIndex::class)->name('discount-groups.index')
        ->middleware('can:viewAny,App\Models\DiscountGroup');
    Route::get('/orders', OrdersIndex::class)->name('orders.index')
        ->middleware('can:viewAny,App\Models\Order');
    Route::get('/orders/{order}/creator', CreatorIndex::class)->name('orders.creator'); // TODO DA CHIEDERE
    Route::get('/orders/{order}/stats', StatsIndex::class)->name('orders.stats')
        ->middleware('can:viewAny,App\Models\Order');
    Route::get('/projects', ProjectsIndex::class)->name('projects.index')
        ->middleware('can:viewAny,App\Models\Project');
    Route::get('/products', ProductsIndex::class)->name('products.index')
        ->middleware('can:viewAny,App\Models\Product');
    Route::get('/collections', CollectionIndex::class)->name('collections.index')
        ->middleware('can:viewAny,App\Models\Collection');
    Route::get('/imports', ImportIndex::class)->name('imports.index');
    Route::get('/exports', ExportIndex::class)->name('exports.index');
    Route::get('/roles', RolesIndex::class)->name('roles.index')
        ->middleware('can:viewAny,Spatie\Permission\Models\Role');

    /*
     * Pages - Create
     */
    Route::get('/users/create', UsersCreate::class)->name('users.create')
        ->middleware('can:create,App\Models\User');
    Route::get('/contacts/create', ContactsCreate::class)->name('contacts.create')
        ->middleware('can:create,App\Models\Contact');
    Route::get('/clients/create', ClientsCreate::class)->name('clients.create')
        ->middleware('can:create,App\Models\Client');
    Route::get('/partners/create', PartnersCreate::class)->name('partners.create')
        ->middleware('can:create,App\Models\Partner');
    Route::get('/suppliers/create', SuppliersCreate::class)->name('suppliers.create')
        ->middleware('can:create,App\Models\Supplier');
    Route::get('/brands/create', BrandsCreate::class)->name('brands.create')
        ->middleware('can:create,App\Models\Brand');
    Route::get('/discount-groups/create', DiscountGroupCreate::class)->name('discount-groups.create')
        ->middleware('can:create,App\Models\DiscountGroup');
    Route::get('/orders/create', OrdersCreate::class)->name('orders.create')
        ->middleware('can:create,App\Models\Order');
    Route::get('/projects/create', ProjectsCreate::class)->name('projects.create')
        ->middleware('can:create,App\Models\Project');
    Route::get('/collections/create', CollectionCreate::class)->name('collections.create')
        ->middleware('can:create,App\Models\Collection');
    Route::get('/imports/create', ImportCreate::class)->name('imports.create');
    Route::get('/roles/create', RolesCreate::class)->name('roles.create')
        ->middleware('can:create,Spatie\Permission\Models\Role');

    /*
     * Pages - Show
     */
    Route::get('/users/{user}', UsersShow::class)->name('users.show')
        ->middleware('can:view,user');
    Route::get('/contacts/{contact}', ContactsShow::class)->name('contacts.show')
        ->middleware('can:view,contact');
    Route::get('/clients/{client}', ClientsShow::class)->name('clients.show')
        ->middleware('can:view,client');
    Route::get('/partners/{partner}', PartnersShow::class)->name('partners.show')
        ->middleware('can:view,partner');
    Route::get('/suppliers/{supplier}', SuppliersShow::class)->name('suppliers.show')
        ->middleware('can:view,supplier');
    Route::get('/brands/{brand}', BrandsShow::class)->name('brands.show')
        ->middleware('can:view,brand');
    Route::get('/discount-groups/{discountGroup}', DiscountGroupShow::class)->name('discount-groups.show')
        ->middleware('can:view,discountGroup');
    Route::get('/collections/{collection}', CollectionShow::class)->name('collections.show')
        ->middleware('can:view,collection');
    Route::get('/orders/{order}', OrdersShow::class)->name('orders.show')
        ->middleware('can:view,order');
    Route::get('/projects/{project}', ProjectsShow::class)->name('projects.show')
        ->middleware('can:view,project');
    Route::get('/roles/{role}', RoleShow::class)->name('roles.show')
        ->middleware('can:view,role');

    /*
     * Pages - Edit
     */
    Route::get('/users/{user}/edit', UsersEdit::class)->name('users.edit')
        ->middleware('can:update,user');
    Route::get('/contacts/{contact}/edit', ContactsEdit::class)->name('contacts.edit')
        ->middleware('can:update,contact');
    Route::get('/clients/{client}/edit', ClientsEdit::class)->name('clients.edit')
        ->middleware('can:update,client');
    Route::get('/partners/{partner}/edit', PartnersEdit::class)->name('partners.edit')
        ->middleware('can:update,partner');
    Route::get('/suppliers/{supplier}/edit', SuppliersEdit::class)->name('suppliers.edit')
        ->middleware('can:update,supplier');
    Route::get('/brands/{brand}/edit', BrandsEdit::class)->name('brands.edit')
        ->middleware('can:update,brand');
    Route::get('/discount-groups/{discountGroup}/edit', DiscountGroupEdit::class)->name('discount-groups.edit')
        ->middleware('can:update,discountGroup');
    Route::get('/orders/{order}/edit', OrdersEdit::class)->name('orders.edit')
        ->middleware('can:update,order');
    Route::get('/projects/{project}/edit', ProjectsEdit::class)->name('projects.edit')
        ->middleware('can:update,project');
    Route::get('/collections/{collection}/edit', CollectionEdit::class)->name('collections.edit')
        ->middleware('can:update,collection');
    Route::get('/roles/{role}/edit', RoleEdit::class)->name('roles.edit')
        ->middleware('can:update,role');

});
