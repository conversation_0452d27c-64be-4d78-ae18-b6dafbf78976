<flux:main container>

    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Create a new project</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the project info and details</flux:subheading>

    <form wire:submit="save" class="flex flex-col gap-12 mt-12">
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Project details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the project info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:date-picker wire:model.live="form.date" label="Date" description="This will be publicly displayed." />
                    <flux:input wire:model="form.name" type="text" placeholder="Name" variant="filled" label="Name" description="This will be publicly displayed." />
                    
                    <flux:select searchable wire:model="form.internal_referent_id" variant="listbox" clearable placeholder="Choose internal referent..." label="Internal Referent" description="This will be publicly displayed." badge="Optional">
                        @foreach($internalReferents as $internalReferent)
                            <flux:select.option value="{{ $internalReferent->id }}">{{ $internalReferent->first_name . ' ' . $internalReferent->last_name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:select searchable wire:model="form.area_manager_id" variant="listbox" clearable placeholder="Choose area manager..." label="Area Manager" description="This will be publicly displayed." badge="Optional">
                        @foreach($areaManagers as $areaManager)
                            <flux:select.option value="{{ $areaManager->id }}">{{ $areaManager->first_name . ' ' . $areaManager->last_name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Extra info</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the project extra info</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:select searchable wire:model.live="form.project_code_region" variant="listbox" clearable placeholder="Choose region..." label="Project Code Region" description="This will be publicly displayed." badge="Optional">
                        @foreach($projectCodeRegions as $projectCodeRegion)
                            <flux:select.option value="{{ $projectCodeRegion->value }}">{{ $projectCodeRegion->label() }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:input wire:model="form.project_code_progressive" readonly type="number" icon="hashtag" placeholder="0" variant="filled" label="Progressive" description="Automatically generated." badge="Readonly" />
                </div>

                <flux:separator variant="subtle" text="select also" class="my-12" />

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:select searchable wire:model.live="form.client_id" variant="listbox" clearable placeholder="Choose client..." label="Client" description="This will be publicly displayed." badge="Optional">
                        @foreach($clients as $client)
                            <flux:select.option value="{{ $client->id }}">{{ $client->company }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:select searchable wire:model="form.partner_id" variant="listbox" clearable placeholder="Choose partner..." label="Partner" description="This will be publicly displayed." badge="Optional">
                        @foreach($partners as $partner)
                            <flux:select.option value="{{ $partner->id }}">{{ $partner->company }}</flux:select.option>
                        @endforeach
                    </flux:select>

                    <flux:select searchable wire:model="form.invoicing_address_id" variant="listbox" clearable placeholder="Choose address..." label="Invoicing Address" description="This will be publicly displayed." badge="Optional">
                        @foreach($invoicingAddresses as $invoicingAddress)
                            <flux:select.option value="{{ $invoicingAddress->id }}">{{ $invoicingAddress->company . ' - ' . $invoicingAddress->name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:select searchable wire:model="form.shipping_address_id" variant="listbox" clearable placeholder="Choose address..." label="Shipping Address" description="This will be publicly displayed." badge="Optional">
                        @foreach($shippingAddresses as $shippingAddress)
                            <flux:select.option value="{{ $shippingAddress->id }}">{{ $shippingAddress->company . ' - ' . $shippingAddress->name }}</flux:select.option>
                        @endforeach
                    </flux:select>

                    <flux:select searchable wire:model="form.payment_term_id" variant="listbox" clearable placeholder="Choose payment terms..." label="Payment Terms" description="This will be publicly displayed." badge="Optional">
                        @foreach($paymentTerms as $paymentTerm)
                            <flux:select.option value="{{ $paymentTerm->id }}">{{ $paymentTerm->code . ' - ' . $paymentTerm->name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Extra info</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the project extra info</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:select searchable wire:model.live="form.type" variant="listbox" clearable placeholder="Choose type..." label="Project Type" description="This will be publicly displayed." badge="Optional">
                        @foreach($types as $type)
                            <flux:select.option value="{{ $type->value }}">{{ $type->label() }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <div></div>

                    @if ($this->form->type)
                        <flux:input wire:model="form.type_name" type="text" placeholder="Type Name" variant="filled" label="{{ $this->form->type instanceOf \App\Enums\ProjectTypes ? $this->form->type->typeNameLabel() : \App\Enums\ProjectTypes::tryFrom($this->form->type)->typeNameLabel() ?? 'Type Name' }}" description="This will be publicly displayed." badge="Optional" />
                        <flux:input wire:model="form.type_link" type="text" placeholder="Type Link" icon="{{ $this->form->type instanceOf \App\Enums\ProjectTypes ? $this->form->type->typeLinkIcon() : \App\Enums\ProjectTypes::tryFrom($this->form->type)->typeLinkIcon() ?? '' }}" variant="filled" label="{{ $this->form->type instanceOf \App\Enums\ProjectTypes ? $this->form->type->typeLinkLabel() : \App\Enums\ProjectTypes::tryFrom($this->form->type)->typeLinkLabel() ?? 'Type Link' }}" description="This will be publicly displayed." badge="Optional" />
                    @endif
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Extra info</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the project extra info</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 gap-4">
                    <flux:editor wire:model="form.description" label="Description" description="This will be publicly displayed." badge="Optional" />
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="flex justify-end gap-4">
            <flux:button @click="$dispatch('redirect-to-projects')" size="sm">Cancel</flux:button>
            <flux:button size="sm" type="submit" variant="primary">Save</flux:button>
        </div>

    </form>

</flux:main>