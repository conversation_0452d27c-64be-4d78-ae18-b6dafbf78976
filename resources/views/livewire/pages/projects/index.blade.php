<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Projects</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the projects list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div>
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div>
                    @can('create', \App\Models\Project::class)
                        <flux:button href="{{ route('projects.create') }}" wire:navigate size="sm">Create project</flux:button>
                    @endcan
                </div>
            </div>
        </div>

        {{-- Projects Listing --}}
        @if ($this->projects->isEmpty())
            <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
        @else
            <flux:table :paginate="$this->projects">
                <flux:table.columns>
                    <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection" wire:click="sort('id')">#</flux:table.column>
                    <flux:table.column sortable :sorted="$sortBy === 'status'" :direction="$sortDirection" wire:click="sort('status')">Status</flux:table.column>
                    <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">Name</flux:table.column>
                    <flux:table.column>Internal Ref.</flux:table.column>
                    <flux:table.column>Client</flux:table.column>
                    <flux:table.column>Partner</flux:table.column>
                    <flux:table.column sortable :sorted="$sortBy === 'project_code'" :direction="$sortDirection" wire:click="sort('project_code')">Project Code</flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @foreach ($this->projects as $project)
                    <flux:table.row :key="$project->id">
                        <flux:table.cell>{{ $project->id }}</flux:table.cell>
                        <flux:table.cell><flux:badge icon="{{ $project->status->icon() }}" color="{{ $project->status->color() }}" size="sm" inset>{{ $project->status->label() }}</flux:badge></flux:table.cell>
                        <flux:table.cell variant="strong">
                            @can('view', $project)
                                <flux:link href="{{ route('projects.show', $project->id) }}" wire:navigate>
                                    {{ $project->name }}
                                </flux:link>
                            @else
                                {{ $project->name }}
                            @endcan
                        </flux:table.cell>
                        <flux:table.cell>{{ $project->internalReferent ? ($project->internalReferent->first_name . ' ' . $project->internalReferent->last_name) : '-' }}</flux:table.cell>
                        <flux:table.cell variant="strong">
                            @if ($project->client)
                                @can('view', $project->client)
                                    <flux:link href="{{ route('clients.show', $project->client->id ?? '') }}" wire:navigate>
                                        {{ $project->client->company ?? '-' }}
                                    </flux:link>
                                @else
                                    {{ $project->client->company ?? '-' }}
                                @endcan
                            @else
                                -
                            @endif
                        </flux:table.cell>
                        <flux:table.cell variant="strong">
                            @if ($project->partner)
                                @can('view', $project->partner)
                                    <flux:link href="{{ route('partners.show', $project->partner->id ?? '') }}" wire:navigate>
                                        {{ $project->partner->company ?? '-' }}
                                    </flux:link>
                                @else
                                    {{ $project->partner->company ?? '-' }}
                                @endcan
                            @else
                                -
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>{{ $project->project_code ?? '-' }}</flux:table.cell>

                        <flux:table.cell class="flex justify-end">
                            <flux:dropdown>
                                <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                <flux:menu>
                                    @can('view', $project)
                                        <flux:menu.item wire:click="view({{ $project->id }})" icon="eye">View</flux:menu.item>
                                    @endcan
                                    @can('update', $project)
                                        <flux:menu.item wire:click="edit({{ $project->id }})" icon="pencil-square">Edit</flux:menu.item>
                                    @endcan

                                    @can('delete', $project)
                                        <flux:menu.separator />
                                        <flux:menu.item wire:confirm="Are you sure you want to delete this project? This action cannot be undone." wire:click="delete({{ $project->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                    @endcan
                                </flux:menu>
                            </flux:dropdown>
                        </flux:table.cell>
                    </flux:table.row>
                    @endforeach
                </flux:table.rows>
            </flux:table>
        @endif
    </div>
</flux:main>
