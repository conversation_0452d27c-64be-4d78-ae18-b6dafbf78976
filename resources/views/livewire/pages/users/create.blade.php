<flux:main container>

    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Create a new user</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the user info and details</flux:subheading>

    <form wire:submit="save" class="flex flex-col gap-12 mt-12">
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">User details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the user info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:input wire:model="form.first_name" type="text" placeholder="<PERSON>" variant="filled" label="First Name" description="This will be publicly displayed." />
                    <flux:input wire:model="form.last_name" type="text" placeholder="Rossi" variant="filled" label="Last Name" description="This will be publicly displayed." />
                    <flux:input wire:model="form.email" type="text" placeholder="<EMAIL>" icon="envelope" variant="filled" label="Email" description="This will be publicly displayed." />
                    <flux:input wire:model="form.phone" type="text" mask="(+99) ***************" badge="Optional" placeholder="(+39) 1231212123" icon="phone" variant="filled" label="Phone" description="This will be publicly displayed." />
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Type and role</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the user info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:checkbox.group label="Employee" description="This will be publicly displayed." variant="cards" class="flex-col">
                        <flux:checkbox wire:model="form.is_employee"
                            icon="building-storefront"
                            label="Internal Employee"
                            description="Check it if the user is an internal employee."
                        />
                    </flux:checkbox.group>
                    <div></div>
                    <flux:select searchable wire:model="form.type" variant="listbox" placeholder="Choose user type..." label="Type" description="This will be publicly displayed.">
                        @foreach($userTypes as $userType)
                            <flux:select.option value="{{ $userType->value }}">{{ $userType->label() }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:select searchable clearable wire:model="form.role" variant="listbox" placeholder="Choose user type..." label="Role" description="This will be publicly displayed.">
                        @foreach($userRoles as $userRole)
                            <flux:select.option value="{{ $userRole->name }}">{{ $userRole->name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Password</flux:heading>
                <flux:subheading size="md" class="mb-6">Set the user login password</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:input wire:model="form.password" type="password" viewable variant="filled" label="Password" description="Enter the user password."/>
                    <flux:input wire:model="form.password_confirmation" type="password" viewable variant="filled" label="Password Confirmation" description="Re-enter the user password."/>
                </div>
            </div>
        </div>

        <div class="flex justify-end gap-4">
            <flux:button @click="$dispatch('redirect-to-users')" size="sm">Cancel</flux:button>
            <flux:button size="sm" type="submit" variant="primary">Save</flux:button>
        </div>

    </form>

</flux:main>
