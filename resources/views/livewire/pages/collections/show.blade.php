<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    {{-- Main Title --}}
    <div class="flex justify-between">
        <div>
            <flux:heading size="xl" level="1">Collection: {{ $collection->name }}</flux:heading>
            <flux:subheading size="lg" class="mb-6">Brand: {{ $collection->brandOfFirstProduct()->name ?? '-' }}</flux:subheading>
        </div>
        <div>
            @if ($this->form->image && Storage::disk(config('filesystems.public'), 'collections')->exists($this->form->image))
                <div class="relative">
                    <img src="{{ Storage::disk(config('filesystems.public'))->url($this->form->image) }}" class="h-24 rounded-lg" />
                </div>
            @elseif ($this->form->image)
                <flux:badge icon="exclamation-triangle" color="yellow">Possible broken link.</flux:badge>
            @else
                <flux:badge icon="information-circle" color="grey">No image uploaded.</flux:badge>
            @endif
        </div>
    </div>

    {{-- Collection Details --}}
    <div class="flex flex-col gap-12 my-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div class="flex items-center">
                </div>
                <div>
                    @canany(['update', 'delete'], $collection)
                        <flux:dropdown>
                            <flux:button size="sm" icon-trailing="chevron-down">Actions</flux:button>
                            <flux:menu>
                                @can('update', $collection)
                                    <flux:menu.item wire:click="edit({{ $collection->id }})" icon="pencil-square">Edit</flux:menu.item>
                                @endcan
                                @can('delete', $collection)
                                    <flux:menu.separator />
                                    <flux:menu.item wire:confirm="Are you sure you want to delete this collection? This action cannot be undone." wire:click="delete({{ $collection->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                @endcan
                            </flux:menu>
                        </flux:dropdown>
                    @endcanany
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Collection details</flux:heading>
                {{-- <flux:subheading size="md" class="mb-6">Here's the collection info and details</flux:subheading> --}}
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:input wire:model="form.code" readonly icon="hashtag" variant="filled" label="Code" description="This will be publicly displayed." />
                    <flux:input wire:model="form.name" readonly variant="filled" label="Name" description="This will be publicly displayed." />

                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

    </div>
</flux:main>
