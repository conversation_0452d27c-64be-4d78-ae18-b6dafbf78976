<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Collections</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the collections list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div>
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div>
                    @can('create', App\Models\Collection::class)
                        <flux:button href="{{ route('collections.create') }}" wire:navigate size="sm">Create collection</flux:button>
                    @endcan
                </div>
            </div>
        </div>

        {{-- Collections Listing --}}
        <flux:table :paginate="$this->collections">
            <flux:table.columns>
                <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection" wire:click="sort('id')">#</flux:table.column>
                <flux:table.column>Image</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'code'" :direction="$sortDirection" wire:click="sort('code')">Code</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">Name</flux:table.column>
                <flux:table.column>Brand</flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @foreach ($this->collections as $collection)
                <flux:table.row :key="$collection->id">
                    <flux:table.cell>{{ $collection->id }}</flux:table.cell>
                    <flux:table.cell>
                        <flux:avatar size="sm" src="{{ Storage::disk(config('filesystems.public'))->url($collection->image ?? '') }}" />
                    </flux:table.cell>
                    <flux:table.cell variant="strong">
                        @can('view', $collection)
                            <flux:link href="{{ route('collections.show', $collection->id) }}" wire:navigate>
                                {{ $collection->code }}
                            </flux:link>
                        @else
                            {{ $collection->code }}
                        @endcan
                    </flux:table.cell>
                    <flux:table.cell>{{ $collection->name }}</flux:table.cell>
                    <flux:table.cell variant="strong">
                        @if ($collection->brandOfFirstProduct())
                            @can('view', $collection->brandOfFirstProduct())
                                <flux:link href="{{ route('brands.show', $collection->brandOfFirstProduct()->id) }}" wire:navigate>
                                    {{ $collection->brandOfFirstProduct()->name ?? '' }}
                                </flux:link>
                            @else
                                {{ $collection->brandOfFirstProduct()->name ?? '' }}
                            @endcan
                        @else
                            -
                        @endif
                    </flux:table.cell>

                    <flux:table.cell class="flex justify-end">
                        <flux:dropdown>
                            <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                            <flux:menu>
                                @can('view', $collection)
                                    <flux:menu.item wire:click="view({{ $collection->id }})" icon="eye">View</flux:menu.item>
                                @endcan
                                @can('update', $collection)
                                    <flux:menu.item wire:click="edit({{ $collection->id }})" icon="pencil-square">Edit</flux:menu.item>
                                @endcan

                                @can('delete', $collection)
                                    <flux:menu.separator />
                                    <flux:menu.item wire:confirm="Are you sure you want to delete this collection? This action cannot be undone." wire:click="delete({{ $collection->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                @endcan
                            </flux:menu>
                        </flux:dropdown>
                    </flux:table.cell>
                </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </div>
</flux:main>
