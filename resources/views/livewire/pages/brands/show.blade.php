<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    {{-- Main Title --}}
    <div class="flex justify-between">
        <div>
            <flux:heading size="xl" level="1">Brand: {{ $brand->name }}</flux:heading>
            <flux:subheading size="lg" class="mb-6">Here's the brand info and details</flux:subheading>
        </div>
        <div>
            @if ($this->form->image && Storage::disk(config('filesystems.public'), 'brands')->exists($this->form->image))
                <div class="relative">
                    <img src="{{ Storage::disk(config('filesystems.public'))->url($this->form->image) }}" alt="Brand logo" class="h-24 rounded-lg" />
                </div>
            @elseif ($this->form->image)
                <flux:badge icon="exclamation-triangle" color="yellow">Possible broken link.</flux:badge>
            @else
                <flux:badge icon="information-circle" color="grey">No image uploaded.</flux:badge>
            @endif
        </div>
    </div>

    {{-- Brand Details --}}
    <div class="flex flex-col gap-12 my-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div class="flex items-center">
                </div>
                <div>
                    @canany(['update', 'delete'], $brand)
                        <flux:dropdown>
                            <flux:button size="sm" icon-trailing="chevron-down">Actions</flux:button>
                            <flux:menu>
                                @can('update', $brand)
                                    <flux:menu.item wire:click="edit({{ $brand->id }})" icon="pencil-square">Edit</flux:menu.item>
                                @endcan
                                @can('delete', $brand)
                                    <flux:menu.separator />
                                    <flux:menu.item wire:confirm="Are you sure you want to delete this brand? This action cannot be undone." wire:click="delete({{ $brand->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                @endcan
                            </flux:menu>
                        </flux:dropdown>
                    @endcanany
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Brand details</flux:heading>
                {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:input wire:model="form.prefix" readonly icon="hashtag" variant="filled" label="Prefix" description="This will be publicly displayed." />
                    <flux:input wire:model="form.name" readonly variant="filled" label="Name" description="This will be publicly displayed." />
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Sales info</flux:heading>
                {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 gap-4">
                    <flux:radio.group disabled wire:model="form.price_range" label="Price Range" variant="segmented" badge="Optional" description="This will be publicly displayed." >
                        @foreach ($brandPriceRanges as $brandPriceRange)
                            <flux:radio value="{{ $brandPriceRange->value }}" label="{{ $brandPriceRange->label() }}" />
                        @endforeach
                    </flux:radio.group>
                    <flux:radio.group disabled wire:model="form.rating" label="Rating" variant="segmented" badge="Optional" description="This will be publicly displayed.">
                        @foreach ($brandRatings as $brandRating)
                            <flux:radio value="{{ $brandRating->value }}" label="{{ $brandRating->label() }}" description="This will be publicly displayed." />
                        @endforeach
                    </flux:radio.group>
                    <flux:radio.group disabled wire:model="form.partnership_level" label="Partnership Level" variant="segmented" badge="Optional" description="This will be publicly displayed.">
                        @foreach ($brandPartnershipLevels as $brandPartnershipLevel)
                            <flux:radio value="{{ $brandPartnershipLevel->value }}" label="{{ $brandPartnershipLevel->label() }}" />
                        @endforeach
                    </flux:radio.group>
                    <div class="hidden md:block">
                        <flux:radio.group wire:model="form.lead_time" disabled variant="segmented" label="Lead Time" badge="Optional" description="This will be publicly displayed.">
                            @foreach ($brandLeadTimes as $brandLeadTime)
                                <flux:radio value="{{ $brandLeadTime->value }}" label="{{ $brandLeadTime->label() }}" />
                            @endforeach
                        </flux:radio.group>
                    </div>
                    <div class="md:hidden">
                        <flux:select searchable wire:model="form.lead_time" disabled variant="listbox" clearable placeholder="Choose lead time..." label="Lead Time" description="This will be publicly displayed." badge="Optional">
                            @foreach ($brandLeadTimes as $brandLeadTime)
                                <flux:select.option value="{{ $brandLeadTime->value }}">{{ $brandLeadTime->label() }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    </div>
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Purchase info</flux:heading>
                {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:select searchable wire:model="form.purchase_price_list" disabled variant="listbox" placeholder="Choose price list..." label="Purchase Price List" badge="Optional" description="This will be publicly displayed.">
                        <flux:select.option>NOIVA</flux:select.option>
                        <flux:select.option>NETPR</flux:select.option>
                        <flux:select.option>IVATO</flux:select.option>
                        <flux:select.option>LISTP</flux:select.option>
                    </flux:select>
                    <flux:input wire:model="form.purchase_conditions" readonly variant="filled" label="Purchase Conditions" badge="Optional" description="This will be publicly displayed." />
                    <flux:input wire:model="form.minimum_orderable" type="number" step="0.01" readonly icon="currency-euro" placeholder="Minimum Orderable" variant="filled" label="Minimum Orderable" badge="Optional" description="This will be publicly displayed." />
                    <flux:input wire:model="form.extra_costs" readonly variant="filled" label="Extra Costs" badge="Optional" description="This will be publicly displayed." />
                    <flux:select searchable wire:model="form.delivery_terms" disabled variant="listbox" clearable placeholder="Choose brand delivery terms..." label="Delivery Terms" description="This will be publicly displayed." badge="Optional">
                        @foreach($deliveryTerms as $deliveryTerm)
                            <flux:select.option value="{{ $deliveryTerm->value }}">{{ $deliveryTerm->label() }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:input wire:model="form.yearly_bonus_info" readonly variant="filled" label="Yearly Bonus Info" badge="Optional" description="This will be publicly displayed." />
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Assets info</flux:heading>
                {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:input wire:model="form.catalogs" readonly copyable icon="link" variant="filled" label="Catalogs" badge="Optional" description="This will be publicly displayed." />
                    <flux:input wire:model="form.pricelist" readonly copyable icon="link" variant="filled" label="Pricelist" badge="Optional" description="This will be publicly displayed." />
                    <flux:input wire:model="form.valid_from" disabled type="date" max="2999-12-31" label="Valid From" badge="Optional" description="This will be publicly displayed." />
                    <flux:input wire:model="form.expected_pricelist_update" disabled type="date" max="2999-12-31" label="Expected Pricelist Update" badge="Optional" description="This will be publicly displayed." />
                    <flux:input wire:model="form.social_link" readonly copyable icon="link" variant="filled" label="Social Link" badge="Optional" description="This will be publicly displayed." />
                    <flux:input wire:model="form.supplier_media_link" readonly copyable icon="link" variant="filled" label="Supplier Media Link" badge="Optional" description="This will be publicly displayed." />
                    <flux:input wire:model="form.supplier_media_link_user" readonly copyable variant="filled" label="Supplier Media Link User" badge="Optional" description="This will be publicly displayed." />
                    <flux:input wire:model="form.supplier_media_link_password" readonly copyable variant="filled" label="Supplier Media Link Password" badge="Optional" description="This will be publicly displayed." />
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Extra info</flux:heading>
                {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 gap-4">
                    <flux:textarea wire:model="form.notes" readonly variant="filled" label="Notes" badge="Optional" description="This will be publicly displayed." />
                </div>
            </div>
        </div>
    </div>

    <flux:separator variant="subtle" class="my-12" />

    {{-- Contacts Listing --}}
    <livewire:components.inner-table.contacts :resourceType="class_basename($brand)" :resourceValue="$brand" >

    <flux:separator variant="subtle" class="my-12" />

    {{-- Clients Listing --}}
    <livewire:components.inner-table.clients :resourceType="class_basename($brand)" :resourceValue="$brand" >

    <flux:separator variant="subtle" class="my-12" />

    {{-- Partners Listing --}}
    <livewire:components.inner-table.partners :resourceType="class_basename($brand)" :resourceValue="$brand" >

    <flux:separator variant="subtle" class="my-12" />

    {{-- Suppliers Listing --}}
    <livewire:components.inner-table.suppliers :resourceType="class_basename($brand)" :resourceValue="$brand" >

    <flux:separator variant="subtle" class="my-12" />

    {{-- Discount Group Listing --}}
    <livewire:components.inner-table.discount-groups :resourceType="class_basename($brand)" :resourceValue="$brand" >

    <flux:separator variant="subtle" class="my-12" />

    {{-- Products Listing --}}
    <livewire:components.inner-table.products :resourceType="class_basename($brand)" :resourceValue="$brand" >

</flux:main>
