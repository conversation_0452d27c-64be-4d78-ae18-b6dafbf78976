<flux:main container>

    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Brands</flux:heading>

    <flux:subheading size="lg" class="mb-6">Here's the brands list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                @if ($this->areFiltersActive())
                <div class="flex justify-start">
                    <flux:button wire:click="clearSearchAndFilters" icon="x-mark" square size="sm" />
                </div>
                @endif
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div>
                    @can('create', \App\Models\Brand::class)
                        <flux:button href="{{ route('brands.create') }}" wire:navigate size="sm">Create brand</flux:button>
                    @endcan
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-4 gap-4">
            <flux:field>
                <flux:select searchable wire:model.live="selectedPriceRange" variant="listbox" size="sm" clearable placeholder="Choose price range...">
                    @foreach ($priceRanges as $priceRange)
                        <flux:select.option value="{{ $priceRange->value }}">{{ $priceRange->label() }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select searchable wire:model.live="selectedRating" variant="listbox" size="sm" clearable placeholder="Choose rating...">
                    @foreach ($ratings as $rating)
                        <flux:select.option value="{{ $rating->value }}">{{ $rating->label() }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select searchable wire:model.live="selectedPartnershipLevel" variant="listbox" size="sm" clearable placeholder="Choose partnership level...">
                    @foreach ($partnershipLevels as $partnershipLevel)
                        <flux:select.option value="{{ $partnershipLevel->value }}">{{ $partnershipLevel->label() }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select searchable wire:model.live="selectedLeadTime" variant="listbox" size="sm" clearable placeholder="Choose lead time...">
                    @foreach ($leadTimes as $leadTime)
                        <flux:select.option value="{{ $leadTime->value }}">{{ $leadTime->label() }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>

            <flux:field>
                <flux:select wire:model.live="selectedTypes" wire:click="fetchAvailableTypes"
                    variant="listbox" size="sm" searchable multiple clearable
                    placeholder="Choose types...">
                    @foreach ($availableTypes as $type)
                        <flux:select.option value="{{ $type }}">{{ strtoupper($type) }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select wire:model.live="selectedMaterials" wire:click="fetchAvailableMaterials"
                    variant="listbox" size="sm" searchable multiple clearable
                    placeholder="Choose materials...">
                    @foreach ($availableMaterials as $material)
                        <flux:select.option value="{{ $material }}">{{ strtoupper($material) }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select wire:model.live="selectedDestinationRooms" wire:click="fetchAvailableDestinationRooms"
                    variant="listbox" size="sm" searchable multiple clearable
                    placeholder="Choose destination rooms...">
                    @foreach ($availableDestinationRooms as $destinationRoom)
                        <flux:select.option value="{{ $destinationRoom }}">{{ strtoupper($destinationRoom) }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
            <flux:field>
                <flux:select wire:model.live="selectedStyles" wire:click="fetchAvailableStyles"
                    variant="listbox" size="sm" searchable multiple clearable
                    placeholder="Choose styles...">
                    @foreach ($availableStyles as $style)
                        <flux:select.option value="{{ $style }}">{{ strtoupper($style) }}</flux:select.option>
                    @endforeach
                </flux:select>
            </flux:field>
        </div>

        <flux:table :paginate="$this->brands">
            <flux:table.columns>
                <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection" wire:click="sort('id')">#</flux:table.column>
                <flux:table.column>Image</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">Brand Name</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'price_range'" :direction="$sortDirection" wire:click="sort('price_range')">Price Range</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'rating'" :direction="$sortDirection" wire:click="sort('rating')">Rating</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'partnership_level'" :direction="$sortDirection" wire:click="sort('partnership_level')">Partnership</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'lead_time'" :direction="$sortDirection" wire:click="sort('lead_time')">Lead Time (Weeks)</flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @foreach ($this->brands as $brand)
                    <flux:table.row :key="$brand->id">
                        <flux:table.cell>{{ $brand->id }}</flux:table.cell>
                        <flux:table.cell>
                            <flux:avatar size="sm" src="{{ Storage::disk(config('filesystems.public'))->url($brand->image ?? '') }}" />
                        </flux:table.cell>
                        <flux:table.cell variant="strong">
                            @can('view', $brand)
                                <flux:link href="{{ route('brands.show', $brand->id) }}" wire:navigate>
                                    {{ $brand->name }}
                                </flux:link>
                            @else
                                {{ $brand->name }}
                            @endcan
                        </flux:table.cell>
                        <flux:table.cell>{{ $brand->price_range ? $brand->price_range->label() : '-' }}</flux:table.cell>
                        <flux:table.cell>{{ $brand->rating ? $brand->rating->label() : '-' }}</flux:table.cell>
                        <flux:table.cell><flux:badge color="{{ $brand->partnership_level ? $brand->partnership_level->color() : 'grey' }}" size="sm" inset>{{ $brand->partnership_level ? $brand->partnership_level->label() : '-' }}</flux:badge></flux:table.cell>
                        <flux:table.cell>{{ $brand->lead_time }}</flux:table.cell>
                        <flux:table.cell class="flex justify-end items-center gap-2">
                            <flux:button href="{{ $brand->catalogs }}" target="_blank" size="sm" icon="book-open" tooltip="Discover Catalog" variant="ghost" x-bind:disabled="! '{{ $brand->catalogs }}'"></flux:button>
                            <flux:button size="sm" icon="folder-plus" tooltip="Request access" variant="ghost" disabled></flux:button>
                            <flux:dropdown>
                                <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                <flux:menu>
                                    @can('view', $brand)
                                        <flux:menu.item wire:click="view({{ $brand->id }})" icon="eye">View</flux:menu.item>
                                    @endcan
                                    @can('update', $brand)
                                        <flux:menu.item wire:click="edit({{ $brand->id }})" icon="pencil-square">Edit</flux:menu.item>
                                    @endcan

                                    @can('delete', $brand)
                                        <flux:menu.separator />
                                        <flux:menu.item wire:confirm="Are you sure you want to delete this brand? This action cannot be undone." wire:click="delete({{ $brand->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                    @endcan
                                </flux:menu>
                            </flux:dropdown>
                        </flux:table.cell>
                    </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </div>
</flux:main>
