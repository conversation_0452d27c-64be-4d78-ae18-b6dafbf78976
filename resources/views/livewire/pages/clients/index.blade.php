<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Clients</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the clients list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div>
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div>
                    @can('create', \App\Models\Client::class)
                        <flux:button href="{{ route('clients.create') }}" wire:navigate size="sm">Create client</flux:button>
                    @endcan
                </div>
            </div>
        </div>

        {{-- Clients Listing --}}
        <flux:table :paginate="$this->clients">
            <flux:table.columns>
                <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection" wire:click="sort('id')">#</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'company'" :direction="$sortDirection" wire:click="sort('company')">Name</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'type'" :direction="$sortDirection" wire:click="sort('type')">Type</flux:table.column>
                <flux:table.column>Internal Ref.</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'commercial_category'" :direction="$sortDirection" wire:click="sort('commercial_category')">Commercial Category</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'priority'" :direction="$sortDirection" wire:click="sort('priority')">Priority</flux:table.column>
                <flux:table.column>Partner</flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @foreach ($this->clients as $client)
                <flux:table.row :key="$client->id">
                    <flux:table.cell>{{ $client->id }}</flux:table.cell>
                    <flux:table.cell variant="strong">
                        @can('view', $client)
                            <flux:link href="{{ route('clients.show', $client->id) }}" wire:navigate>
                                {{ $client->company }}
                            </flux:link>
                        @else
                            {{ $client->company }}
                        @endcan
                    </flux:table.cell>
                    <flux:table.cell><flux:badge icon="{{ $client->type->icon() }}" color="{{ $client->type->color() }}" size="sm" inset>{{ $client->type->label() }}</flux:badge></flux:table.cell>
                    <flux:table.cell>{{ $client->internalReferent ? ($client->internalReferent->first_name . ' ' . $client->internalReferent->last_name) : '-' }}</flux:table.cell>
                    <flux:table.cell>{{ $client->commercial_category ? $client->commercial_category->label() : '-' }}</flux:table.cell>
                    <flux:table.cell>{{ $client->priority ? $client->priority->label() : '-' }}</flux:table.cell>
                    <flux:table.cell variant="strong">
                        @if ($client->partner)
                            @can('view', $client->partner)
                                <flux:link href="{{ route('partners.show', $client->partner->id ?? '') }}" wire:navigate>
                                    {{ $client->partner->company ?? '-' }}
                                </flux:link>
                            @else
                                {{ $client->partner->company ?? '-' }}
                            @endcan
                        @else
                            -
                        @endif
                    </flux:table.cell>

                    <flux:table.cell class="flex justify-end">
                        <flux:dropdown>
                            <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                            <flux:menu>
                                @can('view', $client)
                                    <flux:menu.item wire:click="view({{ $client->id }})" icon="eye">View</flux:menu.item>
                                @endcan
                                @can('update', $client)
                                    <flux:menu.item wire:click="edit({{ $client->id }})" icon="pencil-square">Edit</flux:menu.item>
                                @endcan

                                @can('delete', $client)
                                    <flux:menu.separator />
                                    <flux:menu.item wire:confirm="Are you sure you want to delete this client? This action cannot be undone." wire:click="delete({{ $client->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                @endcan
                            </flux:menu>
                        </flux:dropdown>
                    </flux:table.cell>
                </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </div>
</flux:main>
