<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Create a new supplier</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the supplier info and details</flux:subheading>

    <form wire:submit="save" class="flex flex-col gap-12 mt-12">
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Supplier details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the supplier info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:select searchable wire:model="form.type" variant="listbox" placeholder="Choose type..." label="Type" description="This will be publicly displayed.">
                        @foreach($types as $type)
                            <flux:select.option value="{{ $type->value }}">{{ $type->label() }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <div></div>
                        
                    <flux:input wire:model="form.name" type="text" placeholder="Supplier Name" variant="filled" label="Name" description="This will be publicly displayed." />
                    <flux:input wire:model="form.code" type="text" placeholder="Supplier Code" variant="filled" label="Code" description="This will be publicly displayed." badge="Optional" />
                    
                    <flux:input wire:model="form.payment_conditions" placeholder="Payment Conditions" variant="filled" label="Payment Conditions" badge="Optional" description="This will be publicly displayed." />
                    <flux:radio.group wire:model="form.price_range" label="Price Range" variant="segmented" badge="Optional" description="This will be publicly displayed." >
                        @foreach ($priceRanges as $priceRange)
                            <flux:radio value="{{ $priceRange->value }}" label="{{ $priceRange->label() }}" />
                        @endforeach
                    </flux:radio.group>
                </div>

                <flux:separator variant="subtle" text="select also" class="my-12" />

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <flux:field>
                        <flux:label badge="Optional">Materials</flux:label>
                        <flux:description>This will be publicly displayed.</flux:description>

                        <div class="flex gap-2">
                            <flux:autocomplete wire:model="selectedMaterial" placeholder="Add materials..." clearable>
                                @foreach($materials as $material)
                                    <flux:autocomplete.item wire:key="{{ $material }}">{{ $material }}</flux:autocomplete.item>
                                @endforeach
                            </flux:autocomplete>

                            <flux:button wire:click="addMaterial" icon="plus" class="w-10" />
                        </div>

                        <flux:error name="selectedMaterial" />

                        <div class="flex flex-wrap gap-2 mt-2">
                            @foreach ($form->materials as $material)
                                <flux:badge size="sm">
                                    {{ $material }} <flux:badge.close wire:click="removeMaterial('{{ $material }}')" />
                                </flux:badge>
                            @endforeach
                        </div>
                    </flux:field>
                        
                    <flux:field>
                        <flux:label badge="Optional">Product Types</flux:label>
                        <flux:description>This will be publicly displayed.</flux:description>

                        <div class="flex gap-2">
                            <flux:autocomplete wire:model="selectedProductType" placeholder="Add product types..." clearable>
                                @foreach($productTypes as $productType)
                                    <flux:autocomplete.item wire:key="{{ $productType }}">{{ $productType }}</flux:autocomplete.item>
                                @endforeach
                            </flux:autocomplete>

                            <flux:button wire:click="addProductType" icon="plus" class="w-10" />
                        </div>

                        <flux:error name="selectedProductType" />

                        <div class="flex flex-wrap gap-2 mt-2">
                            @foreach ($form->productTypes as $productType)
                                <flux:badge size="sm">
                                    {{ $productType }} <flux:badge.close wire:click="removeProductType('{{ $productType }}')" />
                                </flux:badge>
                            @endforeach
                        </div>
                    </flux:field>

                    <flux:field>
                        <flux:label badge="Optional">Destination Rooms</flux:label>
                        <flux:description>This will be publicly displayed.</flux:description>

                        <div class="flex gap-2">
                            <flux:autocomplete wire:model="selectedDestinationRoom" placeholder="Add destination rooms..." clearable>
                                @foreach($destinationRooms as $destinationRoom)
                                    <flux:autocomplete.item wire:key="{{ $destinationRoom }}">{{ $destinationRoom }}</flux:autocomplete.item>
                                @endforeach
                            </flux:autocomplete>

                            <flux:button wire:click="addDestinationRoom" icon="plus" class="w-10" />
                        </div>

                        <flux:error name="selectedDestinationRoom" />

                        <div class="flex flex-wrap gap-2 mt-2">
                            @foreach ($form->destinationRooms as $destinationRoom)
                                <flux:badge size="sm">
                                    {{ $destinationRoom }} <flux:badge.close wire:click="removeDestinationRoom('{{ $destinationRoom }}')" />
                                </flux:badge>
                            @endforeach
                        </div>
                    </flux:field>
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Extra info</flux:heading>
                {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
            </div>
            <div class="col-span-2">
                <div class="grid grid-cols-1 gap-4">
                    <flux:textarea wire:model="form.notes" variant="filled" label="Notes" badge="Optional" description="This will be publicly displayed." />
                </div>
            </div>
        </div>

        <div class="flex justify-end gap-4">
            <flux:button @click="$dispatch('redirect-to-suppliers')" size="sm">Cancel</flux:button>
            <flux:button size="sm" type="submit" variant="primary">Save</flux:button>
        </div>
    </form>
</flux:main>
