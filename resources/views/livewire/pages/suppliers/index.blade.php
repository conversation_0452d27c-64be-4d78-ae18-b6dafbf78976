<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Suppliers</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the suppliers list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Search and Actions --}}
        <div class="flex justify-between gap-2">
            <div>
                <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm" />
            </div>
            <div>
                @can('create', \App\Models\Supplier::class)
                    <flux:button href="{{ route('suppliers.create') }}" wire:navigate size="sm">Create supplier</flux:button>
                @endcan
            </div>
        </div>

        {{-- Suppliers Listing --}}
        <flux:table :paginate="$this->suppliers">
            <flux:table.columns>
                <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection" wire:click="sort('id')">#</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'type'" :direction="$sortDirection" wire:click="sort('type')">Type</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">Name</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'code'" :direction="$sortDirection" wire:click="sort('code')">Code</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'price_range'" :direction="$sortDirection" wire:click="sort('price_range')">Price Range</flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @foreach ($this->suppliers as $supplier)
                <flux:table.row :key="$supplier->id">
                    <flux:table.cell>{{ $supplier->id }}</flux:table.cell>
                    <flux:table.cell><flux:badge icon="{{ $supplier->type->icon() }}" color="{{ $supplier->type->color() }}" size="sm" inset>{{ $supplier->type->label() }}</flux:badge></flux:table.cell>
                    <flux:table.cell>
                        @can('view', $supplier)
                            <flux:link href="{{ route('suppliers.show', $supplier->id) }}" wire:navigate>
                                {{ $supplier->name ?? '-' }}
                            </flux:link>
                        @endcan
                    </flux:table.cell>
                    <flux:table.cell>{{ $supplier->code ?? '-' }}</flux:table.cell>
                    <flux:table.cell>{{ $supplier->price_range?->label() ?? '-' }}</flux:table.cell>

                    <flux:table.cell class="flex justify-end">
                        <flux:dropdown>
                            <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions"  variant="ghost"></flux:button>

                            <flux:menu>
                                @can('view', $supplier)
                                    <flux:menu.item wire:click="view({{ $supplier->id }})" icon="eye">View</flux:menu.item>
                                @endcan
                                @can('update', $supplier)
                                    <flux:menu.item wire:click="edit({{ $supplier->id }})" icon="pencil-square">Edit</flux:menu.item>
                                @endcan

                                @can('delete', $supplier)
                                    <flux:menu.separator />
                                    <flux:menu.item wire:confirm="Are you sure you want to delete this supplier? This action cannot be undone." wire:click="delete({{ $supplier->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                @endcan
                            </flux:menu>
                        </flux:dropdown>
                    </flux:table.cell>
                </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </div>
</flux:main>
