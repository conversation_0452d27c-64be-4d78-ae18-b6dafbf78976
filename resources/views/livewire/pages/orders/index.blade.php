<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Orders</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the orders list and details</flux:subheading>

    <div class="flex flex-col gap-8 mt-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div>
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div>
                    @can('create', \App\Models\Order::class)
                        <flux:button href="{{ route('orders.create') }}" wire:navigate size="sm">Create order</flux:button>
                    @endcan
                </div>
            </div>
        </div>

        {{-- Orders Listing --}}
        @if ($this->orders->isEmpty())
            <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
        @else
            <flux:table :paginate="$this->orders">
                <flux:table.columns>
                    <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection" wire:click="sort('id')">#</flux:table.column>
                    <flux:table.column sortable :sorted="$sortBy === 'code'" :direction="$sortDirection" wire:click="sort('code')">Code</flux:table.column>
                    <flux:table.column sortable :sorted="$sortBy === 'status'" :direction="$sortDirection" wire:click="sort('status')">Status</flux:table.column>
                    <flux:table.column>Description</flux:table.column>
                    <flux:table.column>Internal Ref.</flux:table.column>
                    <flux:table.column>Client</flux:table.column>
                    <flux:table.column>Partner</flux:table.column>
                    <flux:table.column sortable :sorted="$sortBy === 'order_code'" :direction="$sortDirection" wire:click="sort('order_code')">Order Code</flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @foreach ($this->orders as $order)
                    <flux:table.row :key="$order->id">
                        <flux:table.cell>{{ $order->id }}</flux:table.cell>
                        <flux:table.cell variant="strong">
                            @can('view', $order)
                                <flux:link href="{{ route('orders.show', $order->id) }}" wire:navigate>
                                    {{ $order->code ?? '-' }}
                                </flux:link>
                            @else
                                {{ $order->code ?? '-' }}
                            @endcan
                        </flux:table.cell>
                        <flux:table.cell><flux:badge icon="{{ $order->status->icon() }}" color="{{ $order->status->color() }}" size="sm" inset>{{ $order->status->label() }}</flux:badge></flux:table.cell>
                        <flux:table.cell>{{ $order->description }}</flux:table.cell>
                        <flux:table.cell>{{ $order->internalReferent ? ($order->internalReferent->first_name . ' ' . $order->internalReferent->last_name) : '-' }}</flux:table.cell>
                        <flux:table.cell variant="strong">
                            @if ($order->client)
                                @can('view', $order->client)
                                    <flux:link href="{{ route('clients.show', $order->client->id ?? '') }}" wire:navigate>
                                        {{ $order->client->company ?? '-' }}
                                    </flux:link>
                                @else
                                    {{ $order->client->company ?? '-' }}
                                @endcan
                            @else
                                -
                            @endif
                        </flux:table.cell>
                        <flux:table.cell variant="strong">
                            @if ($order->partner)
                                @can('view', $order->partner)
                                    <flux:link href="{{ route('partners.show', $order->partner->id ?? '') }}" wire:navigate>
                                        {{ $order->partner->company ?? '-' }}
                                    </flux:link>
                                @else
                                    {{ $order->partner->company ?? '-' }}
                                @endcan
                            @else
                                -
                            @endif
                        </flux:table.cell>
                        <flux:table.cell>{{ $order->order_code }}</flux:table.cell>

                        <flux:table.cell class="flex justify-end">
                            <flux:dropdown>
                                <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                <flux:menu>
                                    @can('view', $order)
                                        <flux:menu.item wire:click="view({{ $order->id }})" icon="eye">View</flux:menu.item>
                                    @endcan
                                    @can('update', $order)
                                        <flux:menu.item wire:click="edit({{ $order->id }})" icon="pencil-square">Edit</flux:menu.item>
                                    @endcan

                                    @can('delete', $order)
                                        <flux:menu.separator />
                                        <flux:menu.item wire:confirm="Are you sure you want to delete this order? This action cannot be undone." wire:click="delete({{ $order->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                    @endcan
                                </flux:menu>
                            </flux:dropdown>
                        </flux:table.cell>
                    </flux:table.row>
                    @endforeach
                </flux:table.rows>
            </flux:table>
        @endif
    </div>
</flux:main>
