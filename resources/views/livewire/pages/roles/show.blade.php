<flux:main container>

    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Role: {{ $role->name }}</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the role info and details</flux:subheading>

    <div class="flex flex-col gap-12 my-12">

        {{-- Actions --}}
        <div class="flex flex-col">
            <div class="flex justify-between gap-2">
                <div class="flex items-center">
                </div>
                <div>
                    @canany(['update', 'delete'], $role)
                        <flux:dropdown>
                            <flux:button size="sm" icon-trailing="chevron-down">Actions</flux:button>
                            <flux:menu>
                                @can('update', $role)
                                    <flux:menu.item wire:click="edit({{ $role->id }})" icon="pencil-square">Edit</flux:menu.item>
                                @endcan
                                @can('delete', $role)
                                    <flux:menu.separator />
                                    <flux:menu.item wire:confirm="Are you sure you want to delete this role? This action cannot be undone." wire:click="delete({{ $role->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                @endcan
                            </flux:menu>
                        </flux:dropdown>
                    @endcanany
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Role details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the role info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:input wire:model="form.name" readonly type="text" placeholder="Name" variant="filled" label="Name" description="This will be publicly displayed." />
                </div>
            </div>
        </div>

        <flux:separator variant="subtle" />

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Role permission</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the role permission and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:table>
                        <flux:table.columns>
                            <flux:table.column></flux:table.column>
                            <flux:table.column>Read</flux:table.column>
                            <flux:table.column>Write</flux:table.column>
                        </flux:table.columns>

                        <flux:table.rows>
                            @foreach($resources as $resource)
                                <flux:table.row>
                                    <flux:table.cell>
                                        <flux:text class="capitalize">{{ $resource->label() }}</flux:text>
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        <flux:checkbox disabled wire:model="form.permissions.{{ $resource->value }}.read" />
                                    </flux:table.cell>
                                    <flux:table.cell>
                                        <flux:checkbox disabled wire:model="form.permissions.{{ $resource->value }}.write" />
                                    </flux:table.cell>
                                </flux:table.row>
                            @endforeach

                        </flux:table.rows>

                    </flux:table>
                </div>
            </div>
        </div>

    </div>

</flux:main>
