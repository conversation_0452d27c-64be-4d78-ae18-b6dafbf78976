<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" />

    <flux:heading size="xl" level="1">Discount Groups</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the price groups list and details</flux:subheading>

    {{-- Actions --}}
    <div class="flex flex-col gap-8 mt-12">
        <div class="flex justify-between gap-2">
            <div>
                <flux:input wire:model.live="search" placeholder="Search..." clearable  size="sm"/>
            </div>
            <div>
                @can('create', \App\Models\DiscountGroup::class)
                    <flux:button href="{{ route('discount-groups.create') }}" wire:navigate size="sm">Create discount group</flux:button>
                @endcan
            </div>
        </div>

        {{-- Discount Groups Listing --}}
        <flux:table :paginate="$this->discountGroups">
            <flux:table.columns>
                <flux:table.column sortable :sorted="$sortBy === 'id'" :direction="$sortDirection" wire:click="sort('id')">#</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'code'" :direction="$sortDirection" wire:click="sort('code')">Code</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'brand_id'" :direction="$sortDirection" wire:click="sort('brand_id')">Brand</flux:table.column>
                <flux:table.column sortable :sorted="$sortBy === 'discount'" :direction="$sortDirection" wire:click="sort('discount')">Discount in %</flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @foreach ($this->discountGroups as $discountGroup)
                <flux:table.row :key="$discountGroup->id">
                    <flux:table.cell>{{ $discountGroup->id }}</flux:table.cell>
                    <flux:table.cell>{{ $discountGroup->code }}</flux:table.cell>
                    <flux:table.cell variant="strong">
                        @if ($discountGroup->brand)
                            @can('view', $discountGroup->brand)
                                <flux:link href="{{ route('brands.show', $discountGroup->brand->id) }}" wire:navigate>
                                    {{ $discountGroup->brand->name }}
                                </flux:link>
                            @else
                                {{ $discountGroup->brand->name }}
                            @endcan
                        @else
                            -
                        @endif
                    </flux:table.cell>
                    <flux:table.cell>{{ $discountGroup->discount }}</flux:table.cell>

                    <flux:table.cell class="flex justify-end">
                        <flux:dropdown>
                            <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                            <flux:menu>
                                @can('view', $discountGroup)
                                    <flux:menu.item wire:click="view({{ $discountGroup->id }})" icon="eye">View</flux:menu.item>
                                @endcan
                                @can('update', $discountGroup)
                                    <flux:menu.item wire:click="edit({{ $discountGroup->id }})" icon="pencil-square">Edit</flux:menu.item>
                                @endcan

                                @can('delete', $discountGroup)
                                    <flux:menu.separator />
                                    <flux:menu.item wire:confirm="Are you sure you want to delete this discount group? This action cannot be undone." wire:click="delete({{ $discountGroup->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                @endcan
                            </flux:menu>
                        </flux:dropdown>
                    </flux:table.cell>
                </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </div>
</flux:main>
