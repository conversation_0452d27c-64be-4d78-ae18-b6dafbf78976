<flux:main container>
    <livewire:components.breadcrumbs />
    <flux:separator variant="subtle" class="my-4" /> 

    <flux:heading size="xl" level="1">Create a new discount group</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the price group info and details</flux:subheading>

    <form wire:submit="save" class="flex flex-col gap-12 mt-12">
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Discount group details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the price group info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <flux:input wire:model="form.code" type="text" placeholder="Code" variant="filled" label="Code" description="Code of the discount group." />
                    <flux:input wire:model="form.discount" type="number" step="0.01" icon="receipt-percent" placeholder="Discount Percentage" variant="filled" label="Discount" description="Enter the discount percentage." />
                    <flux:select searchable wire:model="form.brand_id" variant="listbox" searchable placeholder="Choose brand..." label="Brand" description="Enter the brand." badge="Optional">
                        @foreach($brands as $brand)
                            <flux:select.option value="{{ $brand->id }}">{{ $brand->name }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <flux:input wire:model="form.description" type="text" placeholder="Description" variant="filled" label="Description" description="Description of the discount group." badge="Optional" />
                </div>
            </div>
        </div>

        <div class="flex justify-end gap-4">
            <flux:button @click="$dispatch('redirect-to-discount-groups')" size="sm">Cancel</flux:button>
            <flux:button size="sm" type="submit" variant="primary">Save</flux:button>
        </div>
    </form>
</flux:main>