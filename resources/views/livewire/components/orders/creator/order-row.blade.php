<flux:table.row x-sort:item="{{ $row->id }}">
    @if ($orderRowForm->isEditMode && $orderRowForm->orderRow->id === $row->id)
        {{-- Edit Row --}}
        <flux:table.cell></flux:table.cell>
        <flux:table.cell class="w-16">
            @if ($row->product)
                <flux:avatar size="xl" src="{{ Storage::disk(config('filesystems.public'))->url($row->product->image ?? '') }}" />
            @elseif ($row->customProduct)
                <div class="relative">
                    @if ($this->customProductForm->image)
                        <div class="relative">
                            <flux:avatar size="xl" src="{{ Storage::disk(config('filesystems.public'))->url($this->customProductForm->image ?? '') }}" />
                            <div class="absolute inset-0 flex items-center justify-center">
                                <flux:button wire:click="unsetImage" size="sm" icon="x-mark" />
                            </div>
                        </div>
                    @elseif ($this->customProductForm->image && $this->customProductForm->image instanceof \Illuminate\Http\UploadedFile && $this->customProductForm->image->temporaryUrl())
                        <div class="relative">
                            <img src="{{ $this->customProductForm->image->temporaryUrl() }}" class="w-full rounded-lg" />
                            <div class="absolute inset-0 flex items-center justify-center">
                                <flux:button wire:click="unsetImage" size="sm" icon="x-mark" />
                            </div>
                        </div>
                    @else
                        <div class="absolute inset-0 flex items-center justify-center">
                            <flux:input type="file" wire:model.live="customProductForm.image" id="customProductImage-{{ $row->id }}" class="hidden"/>
                            <flux:button size="sm" icon="plus-circle" variant="ghost" onclick="document.getElementById('customProductImage-{{ $row->id }}').click()" />
                        </div>
                    @endif
                </div>
            @endif
        </flux:table.cell>
        <flux:table.cell>
            @if ($row->product)
                <span class="font-bold">{{ $row->product->brand->name ?? '-' }}</span> <br>
                <flux:heading size="lg">{{ $row->description ?? $row->product->description ?? '-' }}</flux:heading>
                <span class="italic">{{ $row->sku ?? ($row->product->hasModules() ? $row->getModularSku() : $row->product->sku) ?? '-' }}</span>
            @elseif ($row->customProduct)
                <div class="flex flex-col gap-2">
                    <div class="flex items-center gap-2">
                        <flux:text variant="strong">SKU:</flux:text>
                        <flux:input readonly wire:model="customProductForm.sku" type="text" placeholder="Edit SKU" size="xs" variant="filled" />
                    </div>
                    <flux:error name="customProductForm.sku" />

                    <div class="flex items-center gap-2">
                        <flux:text variant="strong">Description:</flux:text>
                        <flux:input wire:model="customProductForm.description" type="text" placeholder="Edit description" size="xs" variant="filled" />
                    </div>
                    <flux:error name="customProductForm.description" />

                    <div class="flex items-center gap-2">
                        <flux:text variant="strong">Brand:</flux:text>
                        <flux:input wire:model="customProductForm.brand_name" type="text" placeholder="Edit brand name" size="xs" variant="filled" />
                    </div>
                    <flux:error name="customProductForm.brand_name" />

                    <div class="flex items-center gap-2">
                        <flux:text variant="strong">Dimensions:</flux:text>
                        <flux:input wire:model="customProductForm.dimensions" type="text" placeholder="Edit dimensions" size="xs" variant="filled" />
                    </div>
                    <flux:error name="customProductForm.dimensions" />

                    <div class="flex items-center gap-2">
                        <flux:text variant="strong">Supplier color:</flux:text>
                        <flux:input wire:model="customProductForm.supplier_color" type="text" placeholder="Edit supplier color" size="xs" variant="filled" />
                    </div>
                    <flux:error name="customProductForm.supplier_color" />
                </div>
            @endif
        </flux:table.cell>
        <flux:table.cell>
            <flux:input wire:model="orderRowForm.quantity" type="number" placeholder="Edit quantity" size="sm" variant="filled" />
            <flux:error name="orderRowForm.quantity" />
        </flux:table.cell>
        <flux:table.cell>
            <flux:input wire:model="orderRowForm.price_override" type="number" step="0.01" placeholder="{{ $row->sellingPrice ? eu_currency($row->sellingPrice) : eu_currency($row->product->sellingPrice ?? 0) }}" size="sm" variant="filled" />
            <flux:error name="orderRowForm.price_override" />
        </flux:table.cell>
        <flux:table.cell>
            <flux:input wire:model="orderRowForm.discount_override" type="number" step="0.01" placeholder="{{ $row->discount ?? $row->getDiscountForClient() }} %" size="sm" variant="filled" />
            <flux:error name="orderRowForm.discount_override" />
        </flux:table.cell>
        <flux:table.cell>
            <flux:input wire:model="orderRowForm.variation" type="number" step="0.01" placeholder="Edit variation" size="sm" variant="filled" />
            <flux:error name="orderRowForm.variation" />
        </flux:table.cell>
        <flux:table.cell>
            <flux:date-picker wire:model="orderRowForm.expected_delivery_date" clearable placeholder="Expected Delivery" size="sm" />
            <flux:error name="orderRowForm.expected_delivery_date" />
        </flux:table.cell>
        <flux:table.cell>{{ eu_currency($row->rowUnitPrice) }}</flux:table.cell>
        <flux:table.cell>{{ eu_currency($row->rowFinalPrice) }}</flux:table.cell>

        {{-- Actions --}}
        <flux:table.cell>
            <div class="flex justify-end gap-2">
                <flux:button type="button" wire:click="updateRow" size="sm" variant="subtle" icon="check" class="-mr-1" />
                <flux:button type="button" wire:click="cancelEditRow" size="sm" variant="subtle" icon="x-mark" class="" />
            </div>
        </flux:table.cell>
    @else
        {{-- View Row --}}
        <flux:table.cell>
            <flux:icon.chevron-up-down variant="outline" class="text-grey-500 dark:text-grey-300" />
        </flux:table.cell>
        <flux:table.cell>
            @if ($row->product)
                <flux:avatar size="xl" src="{{ Storage::disk(config('filesystems.public'))->url($row->product->image ?? '') }}" />
            @elseif ($row->customProduct)
                <flux:avatar size="xl" src="{{ Storage::disk(config('filesystems.public'))->url($row->customProduct->image ?? '') }}" />
            @endif
        </flux:table.cell>
        <flux:table.cell>
            <div class="flex flex-col gap-2" x-data="{ open: false }">
                <div class="flex items-center gap-2">
                    <div>
                        <flux:button x-on:click="open = !open" variant="ghost" size="sm" square x-bind:disabled="! '{{ $row->product?->hasModules() ?? false }}'">
                            @if ($row->product?->deleted_at != null)
                            <flux:icon.exclamation-triangle variant="outline" class="w-6 h-6 text-amber-500 dark:text-amber-300" />
                            @else
                            <flux:icon.information-circle variant="outline" class="w-6 h-6 text-grey-500 dark:text-grey-300" />
                            @endif
                        </flux:button>
                    </div>
                    <div>
                        @if ($row->product)
                            <span class="font-bold">{{ $row->product->brand->name ?? '-' }}</span> <br>
                            <flux:heading size="lg">{{ $row->description ?? $row->product->description ?? '-' }}</flux:heading>
                            <span class="italic">{{ $row->sku ?? ($row->product->hasModules() ? $row->getModularSku() : $row->product->sku) ?? '-' }}</span>
                        @elseif ($row->customProduct)
                            <span class="font-bold">{{ $row->customProduct->brand_name ?? '-' }}</span> <br>
                            <flux:heading size="lg">{{ $row->description ?? $row->customProduct->description ?? '-' }}</flux:heading>
                            <span class="italic">{{ $row->sku ?? $row->customProduct->sku ?? '-' }}</span>
                        @endif

                        @if ($row->product?->hasModules())
                            <div x-show="open" class="flex flex-col gap-2 mt-4">
                                <flux:heading>Collection: {{ $row->product->collection->name ?? '-' }}</flux:heading>
                                <flux:heading>Color: {{ $row->product->supplier_color ?? '-' }}</flux:heading>
                                <flux:heading>Dimensions: {{ $row->product->dimensions ?? '-' }}</flux:heading>

                                @if ($row->options)
                                    <div class="flex flex-col">
                                        <flux:heading class="mb-0!">Options:</flux:heading>
                                        @foreach ($row->getSelectedOptions() as $option)
                                            <div class="flex items-center gap-2">
                                                @if ($option['image'])
                                                    <img src="{{ Storage::disk(config('filesystems.public'))->url($option['image']) }}"
                                                        alt="{{ $option['description'] }}" class="w-4 object-cover rounded-full" />
                                                @else
                                                    {{-- Image url not in db --}}
                                                    <img src="{{ asset('no-image.jpg') }}" class="w-4 object-cover rounded-full">
                                                @endif
                                                <flux:subheading>{{ $option['description'] }}</flux:subheading>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </flux:table.cell>
        <flux:table.cell>{{ $row->quantity }}</flux:table.cell>

        @if ($row->selling_price_override)
            <flux:table.cell>
                <div class="flex items-center gap-2">
                    {{ eu_currency($row->sellingPriceOverride) }}
                    <flux:icon.pencil variant="solid" class="text-cyan-500 dark:text-cyan-300 size-4" />
                <div>
            </flux:table.cell>
        @else
            <flux:table.cell>{{ $row->sellingPrice ? eu_currency($row->sellingPrice) : ($row->product?->hasModules() ? eu_currency($row->product?->getModularPrice($row->options)) : eu_currency($row->product->sellingPrice ?? 0)) }}</flux:table.cell>
        @endif

        @if (isset($row->discount_override))
            <flux:table.cell>
                <div class="flex items-center gap-2">
                    {{ $row->discount_override }} %
                    <flux:icon.pencil variant="solid" class="text-cyan-500 dark:text-cyan-300 size-4" />
                <div>
            </flux:table.cell>
        @else
            <flux:table.cell>{{ $row->discount ?? $row->getDiscountForClient() }} %</flux:table.cell>
        @endif
        <flux:table.cell>{{ $row->variation ?? '--' }} %</flux:table.cell>
        <flux:table.cell>{{ $row->expected_delivery_date?->format('d-m-y') ?? '-' }}</flux:table.cell>
        <flux:table.cell>{{ eu_currency($row->rowUnitPrice) }}</flux:table.cell>
        <flux:table.cell>{{ eu_currency($row->rowFinalPrice) }}</flux:table.cell>

        {{-- Actions Dropdown --}}
        <flux:table.cell>
            <div class="flex justify-end">
                <flux:dropdown>
                    <flux:button size="sm" icon="ellipsis-vertical" square />

                    <flux:menu>
                        <flux:menu.item wire:click="" icon="eye" disabled>View</flux:menu.item>
                        @can('update',$resourceValue)
                            <flux:menu.item wire:click="editRow({{ $row?->id }})" icon="pencil-square">Edit</flux:menu.item>
                        @endcan
                        @can('delete',$resourceValue)
                            <flux:menu.separator />
                            <flux:menu.item wire:click="deleteRow({{ $row?->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                        @endcan
                    </flux:menu>
                </flux:dropdown>
            </div>
        </flux:table.cell>
    @endif
</flux:table.row>
