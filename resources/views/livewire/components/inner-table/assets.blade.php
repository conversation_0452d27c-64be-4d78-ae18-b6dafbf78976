<div>
    {{-- Assets Listing --}}
    <div class="flex flex-col">
        <div class="flex justify-between">
            <div class="flex flex-col">
                <flux:heading size="lg" level="1">Assets</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the assets for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex items-center gap-2">
                @can('create', $resourceValue )
                    <flux:modal.trigger name="create-asset">
                        <flux:button size="sm">Create asset</flux:button>
                    </flux:modal.trigger>
                @endcan
            </div>
        </div>

        <div>
            @if ($this->assets->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->assets">
                    <flux:table.columns>
                        <flux:table.column>Name</flux:table.column>
                        <flux:table.column>Type</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->assets as $asset)
                        <flux:table.row>
                            <flux:table.cell variant="strong">{{ $asset->name }}</flux:table.cell>
                            <flux:table.cell>{{ $asset->type->label() }}</flux:table.cell>

                            <flux:table.cell class="flex justify-end">
                                <flux:button href="{{ $asset->link }}" target="_blank" size="sm" icon="arrow-top-right-on-square" tooltip="Open Link" variant="ghost" x-bind:disabled="! '{{ $asset->link }}'"></flux:button>
                                <flux:button wire:click="downloadFile({{ $asset->id }})" size="sm" icon="folder-arrow-down" tooltip="Download File" variant="ghost" x-bind:disabled="! '{{ $asset->file }}'"></flux:button>

                                @can('create', $resourceValue )
                                    <flux:dropdown>
                                        <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>
                                            <x-inner-table.row-crud-menu :item="$asset" />
                                    </flux:dropdown>
                                @endcan

                            </flux:table.cell>
                        </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    {{-- View Asset - Modal --}}
    <flux:modal name="view-asset" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">View asset</flux:heading>
            <flux:subheading>View asset for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <div class="flex flex-col gap-6">
            <flux:input wire:model="form.name" readonly type="text" placeholder="Mario Rossi" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="form.link" readonly copyable icon="link" variant="filled" label="Link" badge="Optional" description="This will be publicly displayed." />
        </div>
    </flux:modal>

    {{-- Create Asset - Modal --}}
    <flux:modal name="create-asset" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">Create asset</flux:heading>
            <flux:subheading>Create asset for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <form wire:submit="create" class="flex flex-col gap-6">
            <flux:input wire:model="form.name" type="text" placeholder="Mario Rossi" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="form.link" copyable icon="link" variant="filled" label="Link" badge="Optional" description="This will be publicly displayed." />

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <flux:input wire:model="form.file" type="file" size="sm" label="File" description="Upload the file. (max 10MB)" badge="Optional" />

                @if ($this->form->file)
                    <x-files.preview-card unsetAction="unsetFile" />
                @endif
            </div>

            <div class="flex">
                <flux:spacer />
                @can('create', $resourceValue )
                    <flux:button size="sm" type="submit" variant="primary">Create asset</flux:button>
                @endcan
            </div>
        </form>
    </flux:modal>

    {{-- Edit Asset - Modal --}}
    <flux:modal name="edit-asset" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">Edit asset</flux:heading>
            <flux:subheading>Edit asset for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <form wire:submit="update" class="flex flex-col gap-6">
            <flux:input wire:model="form.name" type="text" placeholder="Mario Rossi" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="form.link" copyable icon="link" variant="filled" label="Link" badge="Optional" description="This will be publicly displayed." />

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <flux:input wire:model="form.file" type="file" size="sm" label="File" description="Upload the file. (max 10MB)" badge="Optional" />

                @if ($this->form->file)
                    <x-files.preview-card unsetAction="unsetFile" />
                @endif
            </div>

            <div class="flex">
                <flux:spacer />
                @can('update', $resourceValue )
                    <flux:button size="sm" type="submit" variant="primary">Edit asset</flux:button>
                @endcan
            </div>
        </form>
    </flux:modal>
</div>
