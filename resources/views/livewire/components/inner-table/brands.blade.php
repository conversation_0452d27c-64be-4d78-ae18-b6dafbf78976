<div>
    {{-- Brands Listing --}}
    <div class="flex flex-col">
        <div class="flex justify-between">
            <div class="flex flex-col">
                <flux:heading size="lg" level="1">Brands</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the brands for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex items-center gap-2">
                <flux:modal.trigger name="attach-brand">
                    <flux:button size="sm">Attach brand</flux:button>
                </flux:modal.trigger>
            </div>
        </div>

        <div>
            @if ($this->brands->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->brands">
                    <flux:table.columns>
                        <flux:table.column>Image</flux:table.column>
                        <flux:table.column>Brand Name</flux:table.column>
                        <flux:table.column>Price Range</flux:table.column>
                        <flux:table.column>Rating</flux:table.column>
                        <flux:table.column>Partnership</flux:table.column>
                        <flux:table.column>Lead Time (Weeks)</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->brands as $brand)
                        <flux:table.row :key="$brand->id">
                            <flux:table.cell>
                                @if ($brand->image && Storage::disk(config('filesystems.public'), 'brands')->exists($brand->image))
                                    <img src="{{ Storage::disk(config('filesystems.public'))->url($brand->image) }}" alt="{{ $brand->name }}" class="h-6"/>
                                @elseif ($brand->image)
                                    <flux:tooltip content="Possible broken link." position="left">
                                        <flux:icon.exclamation-triangle variant="outline" class="text-amber-500 dark:text-amber-300" />
                                    </flux:tooltip>
                                @else
                                    <flux:tooltip content="No image uploaded." position="left">
                                        <flux:icon.information-circle variant="outline" class="text-grey-500 dark:text-grey-300" />
                                    </flux:tooltip>
                                @endif
                            </flux:table.cell>
                            <flux:table.cell variant="strong">
                                <a href="{{ route('brands.show', $brand->id) }}" wire:navigate>{{ $brand->name }}</a>
                            </flux:table.cell>
                            <flux:table.cell>{{ $brand->price_range ? $brand->price_range->label() : '-' }}</flux:table.cell>
                            <flux:table.cell>{{ $brand->rating ? $brand->rating->label() : '-' }}</flux:table.cell>
                            <flux:table.cell><flux:badge color="{{ $brand->partnership_level ? $brand->partnership_level->color() : 'grey' }}" size="sm" inset>{{ $brand->partnership_level ? $brand->partnership_level->label() : '-' }}</flux:badge></flux:table.cell>
                            <flux:table.cell>{{ $brand->lead_time }}</flux:table.cell>
                            <flux:table.cell class="flex justify-end items-center gap-2">
                                <flux:button href="{{ $brand->catalogs }}" target="_blank" size="sm" icon="book-open" tooltip="Discover catalog"></flux:button>
                                <flux:button size="sm" icon="folder-plus" tooltip="Request access" variant="subtle" disabled></flux:button>
                                <flux:dropdown>
                                    <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                    <flux:menu>
                                        <flux:menu.item wire:click="view({{ $brand->id }})" icon="eye">View</flux:menu.item>
                                        {{-- <flux:menu.item wire:click="edit({{ $brand->id }})" icon="pencil-square">Edit</flux:menu.item> --}}

                                        <flux:menu.separator />

                                        <flux:menu.item wire:confirm="Are you sure you want to detach this brand? This will not delete the brand itself." wire:click="detach({{ $brand->id }})" variant="danger" icon="x-mark">Detach</flux:menu.item>
                                    </flux:menu>
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    {{-- Attach Brand - Modal --}}
    <flux:modal name="attach-brand" variant="flyout" class="space-y-12" x-data="{ selectedBrandId: null }" x-on:close="selectedBrandId = null" :dismissible="false">
        <div>
            <flux:heading size="lg">Attach brand</flux:heading>
            <flux:subheading>Attach brand to the supplier.</flux:subheading>
        </div>

        <flux:select searchable x-model="selectedBrandId" variant="listbox" searchable clearable placeholder="Choose brand...">
            @foreach($brands as $brand)
                <flux:select.option value="{{ $brand->id }}">{{ $brand->name }}</flux:select.option>
            @endforeach
        </flux:select>

        <div class="flex">
            <flux:spacer />
            <flux:button @click="$wire.attach(selectedBrandId)" type="submit" variant="primary" size="sm">Attach brand</flux:button>
        </div>
    </flux:modal>
</div>
