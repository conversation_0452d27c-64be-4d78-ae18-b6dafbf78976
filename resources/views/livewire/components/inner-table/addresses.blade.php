<div>
    {{-- Addresses Listing --}}
    <div class="flex flex-col">
        <div class="flex justify-between">
            <div class="flex flex-col">
                <flux:heading size="lg" level="1">Addresses</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the addresses for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex items-center gap-2">
                @can('create', $resourceValue)
                    <flux:modal.trigger name="create-address">
                        <flux:button size="sm">Create address</flux:button>
                    </flux:modal.trigger>
                @endcan
            </div>
        </div>

        <div>
            @if ($this->addresses->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->addresses">
                    <flux:table.columns>
                        <flux:table.column>Name</flux:table.column>
                        <flux:table.column>Type</flux:table.column>
                        <flux:table.column>Company</flux:table.column>
                        <flux:table.column>Country</flux:table.column>
                        <flux:table.column>Related Invoicing</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->addresses as $address)
                        <flux:table.row>
                            <flux:table.cell variant="strong">{{ $address->name }}</flux:table.cell>
                            <flux:table.cell><flux:badge icon="{{ $address->type->icon() }}" color="{{ $address->type->color() }}" size="sm" inset>{{ $address->type->label() }}</flux:badge></flux:table.cell>
                            <flux:table.cell>{{ $address->company ?? '-' }}</flux:table.cell>
                            <flux:table.cell>{{ $address->country ? str_replace('_', ' ', $address->country->name) : '-' }}</flux:table.cell>
                            <flux:table.cell>{{ $address->parent ? $address->parent->company : '-' }}</flux:table.cell>

                            <flux:table.cell class="flex justify-end">
                                <flux:dropdown>
                                    <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                    <flux:menu>
                                        @can('view', $resourceValue)
                                            <flux:menu.item wire:click="view({{ $address->id }})" icon="eye">View</flux:menu.item>
                                        @endcan
                                        @can('update', $resourceValue)
                                            <flux:menu.item wire:click="edit({{ $address->id }})" icon="pencil-square">Edit</flux:menu.item>
                                        @endcan
                                        @can('delete', $resourceValue)
                                            <flux:menu.separator />
                                            <flux:menu.item wire:confirm="Are you sure you want to delete this address? This action cannot be undone." wire:click="delete({{ $address->id }})" variant="danger" icon="trash">Delete</flux:menu.item>
                                        @endcan
                                    </flux:menu>
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    {{-- View Address - Modal --}}
    <flux:modal name="view-address" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">View address</flux:heading>
            <flux:subheading>View address for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <div class="flex flex-col gap-6">
            <flux:input wire:model="form.name" readonly type="text" placeholder="HeadQuarter" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:select searchable wire:model="form.type" disabled variant="listbox" placeholder="Choose type..." label="Type" description="This will be publicly displayed.">
                @foreach($addressTypes as $addressType)
                    <flux:select.option value="{{ $addressType->value }}">{{ $addressType->label() }}</flux:select.option>
                @endforeach
            </flux:select>

            @if ($form->type == 'shipping')
                <flux:select searchable wire:model="form.parent_id" disabled variant="listbox" searchable placeholder="Choose the invoicing address..." label="Invoicing Address" description="This will be publicly displayed." badge="Optional">
                    @foreach($invoicingAddresses as $invoicingAddress)
                        <flux:select.option value="{{ $invoicingAddress->id }}">{{ $invoicingAddress->name . ' - ' . $invoicingAddress->company }}</flux:select.option>
                    @endforeach
                </flux:select>
                <flux:input wire:model="form.code_shipping" readonly type="text" placeholder="*********" variant="filled" label="Code" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.company" readonly type="text" placeholder="Green SRL" variant="filled" label="Company/Description" description="This will be publicly displayed." />
                <flux:input wire:model="form.street" readonly type="text" placeholder="Corso Venezia, 11" variant="filled" label="Street" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.city" readonly type="text" placeholder="Milan" variant="filled" label="City" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.state" readonly type="text" placeholder="Milan" variant="filled" label="State" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.zip" readonly type="text" placeholder="20122" variant="filled" label="ZIP" description="This will be publicly displayed." badge="Optional" />
                <flux:select searchable wire:model="form.country" disabled variant="listbox" searchable placeholder="Choose the country..." label="Country" description="This will be publicly displayed." badge="Optional">
                    @foreach($countries as $country)
                        <flux:select.option value="{{ $country->value }}">{{ str_replace('_', ' ', $country->name) }}</flux:select.option>
                    @endforeach
                </flux:select>
            @elseif ($form->type == 'invoicing')
                <flux:input wire:model="form.code_invoicing" readonly type="text" placeholder="*********" variant="filled" label="Code" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.company" readonly type="text" placeholder="Green SRL" variant="filled" label="Company" description="This will be publicly displayed." />
                <flux:input wire:model="form.vat_number" readonly type="text" placeholder="VAT Number" variant="filled" label="VAT Number" badge="Optional" description="This will be publicly displayed." />
                <flux:input wire:model="form.fiscal_code" readonly type="text" placeholder="Fiscal Code" variant="filled" label="Fiscal Code" badge="Optional" description="This will be publicly displayed." />
                <flux:input wire:model="form.sdi_code" readonly type="text" placeholder="SDI Code" variant="filled" label="SDI Code" badge="Optional" description="This will be publicly displayed." />
                <flux:input wire:model="form.street" readonly type="text" placeholder="Corso Venezia, 11" variant="filled" label="Street" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.city" readonly type="text" placeholder="Milan" variant="filled" label="City" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.state" readonly type="text" placeholder="Milan" variant="filled" label="State" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.zip" readonly type="text" placeholder="20122" variant="filled" label="ZIP" description="This will be publicly displayed." badge="Optional" />
                <flux:select searchable wire:model="form.country" disabled variant="listbox" searchable placeholder="Choose the country..." label="Country" description="This will be publicly displayed." badge="Optional">
                    @foreach($countries as $country)
                        <flux:select.option value="{{ $country->value }}">{{ str_replace('_', ' ', $country->name) }}</flux:select.option>
                    @endforeach
                </flux:select>
            @endif
        </div>
    </flux:modal>

    {{-- Create Address - Modal --}}
    <flux:modal name="create-address" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">Create address</flux:heading>
            <flux:subheading>Create address for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <form wire:submit="create" class="flex flex-col gap-6">
            <flux:input wire:model="form.name" type="text" placeholder="HeadQuarter" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:select searchable wire:model.live="form.type" variant="listbox" placeholder="Choose type..." label="Type" description="This will be publicly displayed.">
                @foreach($addressTypes as $addressType)
                    <flux:select.option value="{{ $addressType->value }}">{{ $addressType->label() }}</flux:select.option>
                @endforeach
            </flux:select>

            @if ($form->type == 'shipping')
                <flux:select searchable wire:model="form.parent_id" variant="listbox" searchable placeholder="Choose the invoicing address..." label="Invoicing Address" description="This will be publicly displayed." badge="Optional">
                    @foreach($invoicingAddresses as $invoicingAddress)
                        <flux:select.option value="{{ $invoicingAddress->id }}">{{ $invoicingAddress->name . ' - ' . $invoicingAddress->company }}</flux:select.option>
                    @endforeach
                </flux:select>
                <flux:input wire:model="form.code_shipping" type="text" placeholder="*********" variant="filled" label="Code" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.company" type="text" placeholder="Green SRL" variant="filled" label="Company/Description" description="This will be publicly displayed." />
                <flux:input wire:model="form.street" type="text" placeholder="Corso Venezia, 11" variant="filled" label="Street" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.city" type="text" placeholder="Milan" variant="filled" label="City" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.state" type="text" placeholder="Milan" variant="filled" label="State" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.zip" type="text" placeholder="20122" variant="filled" label="ZIP" description="This will be publicly displayed." badge="Optional" />
                <flux:select searchable wire:model="form.country" variant="listbox" searchable placeholder="Choose the country..." label="Country" description="This will be publicly displayed." badge="Optional">
                    @foreach($countries as $country)
                        <flux:select.option value="{{ $country->value }}">{{ str_replace('_', ' ', $country->name) }}</flux:select.option>
                    @endforeach
                </flux:select>
            @elseif ($form->type == 'invoicing')
                <flux:input wire:model="form.code_invoicing" type="text" placeholder="*********" variant="filled" label="Code" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.company" type="text" placeholder="Green SRL" variant="filled" label="Company" description="This will be publicly displayed." />
                <flux:input wire:model="form.vat_number" type="text" placeholder="VAT Number" variant="filled" label="VAT Number" badge="Optional" description="This will be publicly displayed." />
                <flux:input wire:model="form.fiscal_code" type="text" placeholder="Fiscal Code" variant="filled" label="Fiscal Code" badge="Optional" description="This will be publicly displayed." />
                <flux:input wire:model="form.sdi_code" type="text" placeholder="SDI Code" variant="filled" label="SDI Code" badge="Optional" description="This will be publicly displayed." />
                <flux:input wire:model="form.street" type="text" placeholder="Corso Venezia, 11" variant="filled" label="Street" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.city" type="text" placeholder="Milan" variant="filled" label="City" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.state" type="text" placeholder="Milan" variant="filled" label="State" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.zip" type="text" placeholder="20122" variant="filled" label="ZIP" description="This will be publicly displayed." badge="Optional" />
                <flux:select searchable wire:model="form.country" variant="listbox" searchable placeholder="Choose the country..." label="Country" description="This will be publicly displayed." badge="Optional">
                    @foreach($countries as $country)
                        <flux:select.option value="{{ $country->value }}">{{ str_replace('_', ' ', $country->name) }}</flux:select.option>
                    @endforeach
                </flux:select>
            @endif

            <div class="flex">
                <flux:spacer />
                @can('create', $resourceValue)
                    <flux:button size="sm" type="submit" variant="primary">Create address</flux:button>
                @endcan
            </div>
        </form>
    </flux:modal>

    {{-- Edit Address - Modal --}}
    <flux:modal name="edit-address" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">Edit address</flux:heading>
            <flux:subheading>Edit address for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <form wire:submit="update" class="flex flex-col gap-6">
            <flux:input wire:model="form.name" type="text" placeholder="HeadQuarter" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:select searchable wire:model.live="form.type" variant="listbox" placeholder="Choose type..." label="Type" description="This will be publicly displayed.">
                @foreach($addressTypes as $addressType)
                    <flux:select.option value="{{ $addressType->value }}">{{ $addressType->label() }}</flux:select.option>
                @endforeach
            </flux:select>

            @if ($form->type == 'shipping')
                <flux:select searchable wire:model="form.parent_id" variant="listbox" searchable placeholder="Choose the invoicing address..." label="Invoicing Address" description="This will be publicly displayed." badge="Optional">
                    @foreach($invoicingAddresses as $invoicingAddress)
                        <flux:select.option value="{{ $invoicingAddress->id }}">{{ $invoicingAddress->name . ' - ' . $invoicingAddress->company }}</flux:select.option>
                    @endforeach
                </flux:select>
                <flux:input wire:model="form.code_shipping" type="text" placeholder="*********" variant="filled" label="Code" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.company" type="text" placeholder="Green SRL" variant="filled" label="Company/Description" description="This will be publicly displayed." />
                <flux:input wire:model="form.street" type="text" placeholder="Corso Venezia, 11" variant="filled" label="Street" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.city" type="text" placeholder="Milan" variant="filled" label="City" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.state" type="text" placeholder="Milan" variant="filled" label="State" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.zip" type="text" placeholder="20122" variant="filled" label="ZIP" description="This will be publicly displayed." badge="Optional" />
                <flux:select searchable wire:model="form.country" variant="listbox" searchable placeholder="Choose the country..." label="Country" description="This will be publicly displayed." badge="Optional">
                    @foreach($countries as $country)
                        <flux:select.option value="{{ $country->value }}">{{ str_replace('_', ' ', $country->name) }}</flux:select.option>
                    @endforeach
                </flux:select>
            @elseif ($form->type == 'invoicing')
                <flux:input wire:model="form.code_invoicing" type="text" placeholder="*********" variant="filled" label="Code" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.company" type="text" placeholder="Green SRL" variant="filled" label="Company" description="This will be publicly displayed." />
                <flux:input wire:model="form.vat_number" type="text" placeholder="VAT Number" variant="filled" label="VAT Number" badge="Optional" description="This will be publicly displayed." />
                <flux:input wire:model="form.fiscal_code" type="text" placeholder="Fiscal Code" variant="filled" label="Fiscal Code" badge="Optional" description="This will be publicly displayed." />
                <flux:input wire:model="form.sdi_code" type="text" placeholder="SDI Code" variant="filled" label="SDI Code" badge="Optional" description="This will be publicly displayed." />
                <flux:input wire:model="form.street" type="text" placeholder="Corso Venezia, 11" variant="filled" label="Street" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.city" type="text" placeholder="Milan" variant="filled" label="City" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.state" type="text" placeholder="Milan" variant="filled" label="State" description="This will be publicly displayed." badge="Optional" />
                <flux:input wire:model="form.zip" type="text" placeholder="20122" variant="filled" label="ZIP" description="This will be publicly displayed." badge="Optional" />
                <flux:select searchable wire:model="form.country" variant="listbox" searchable placeholder="Choose the country..." label="Country" description="This will be publicly displayed." badge="Optional">
                    @foreach($countries as $country)
                        <flux:select.option value="{{ $country->value }}">{{ str_replace('_', ' ', $country->name) }}</flux:select.option>
                    @endforeach
                </flux:select>
            @endif

            <div class="flex">
                <flux:spacer />
                @can('update', $resourceValue)
                    <flux:button size="sm" type="submit" variant="primary">Edit address</flux:button>
                @endcan
            </div>
        </form>
    </flux:modal>
</div>
