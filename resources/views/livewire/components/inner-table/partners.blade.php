<div>
    {{-- Partners Listing --}}
    <div class="flex flex-col">
        <div class="flex justify-between">
            <div class="flex flex-col">
                <flux:heading size="lg" level="1">Partners</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the partners for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex items-center gap-2">
                @can('update', $resourceValue)
                    <flux:modal.trigger name="attach-partner">
                        <flux:button size="sm">Attach partner</flux:button>
                    </flux:modal.trigger>
                @endcan
            </div>
        </div>

        <div>
            @if ($this->partners->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->partners">
                    <flux:table.columns>
                        <flux:table.column>Name</flux:table.column>
                        <flux:table.column>Type</flux:table.column>
                        <flux:table.column>Internal Ref.</flux:table.column>
                        <flux:table.column>Commercial Category</flux:table.column>
                        <flux:table.column>Priority</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->partners as $partner)
                        <flux:table.row>
                            <flux:table.cell variant="strong">
                                <a href="{{ route('partners.show', $partner->id) }}" wire:navigate>{{ $partner->company }}</a>
                            </flux:table.cell>
                            <flux:table.cell><flux:badge icon="{{ $partner->type->icon() }}" color="{{ $partner->type->color() }}" size="sm" inset>{{ $partner->type->label() }}</flux:badge></flux:table.cell>
                            <flux:table.cell>{{ $partner->internalReferent ? ($partner->internalReferent->first_name . ' ' . $partner->internalReferent->last_name) : '-' }}</flux:table.cell>
                            <flux:table.cell>{{ $partner->commercial_category ? $partner->commercial_category->label() : '-' }}</flux:table.cell>
                            <flux:table.cell>{{ $partner->priority ? $partner->priority->label() : '-' }}</flux:table.cell>

                            <flux:table.cell class="flex justify-end">
                                <flux:dropdown>
                                    <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                    <flux:menu>
                                        @can('view', $partner)
                                            <flux:menu.item wire:click="view({{ $partner->id }})" icon="eye">View</flux:menu.item>
                                        @endcan
                                        @can('update', $partner)
                                            <flux:menu.item wire:click="edit({{ $partner->id }})" icon="pencil-square">Edit</flux:menu.item>
                                        @endcan
                                        @can('update', $resourceValue)
                                            <flux:menu.separator />
                                            <flux:menu.item wire:confirm="Are you sure you want to detach this partner? This will not delete the partner itself." wire:click="detach({{ $partner->id }})" variant="danger" icon="x-mark">Detach</flux:menu.item>
                                        @endcan
                                    </flux:menu>
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    {{-- Attach Partner - Modal --}}
    <flux:modal name="attach-partner" variant="flyout" class="space-y-12" x-data="{ selectedPartnerId: null }" x-on:close="selectedPartnerId = null" :dismissible="false">
        <div>
            <flux:heading size="lg">Attach partner</flux:heading>
            <flux:subheading>Attach partner to the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <flux:select searchable x-model="selectedPartnerId" variant="listbox" searchable clearable placeholder="Choose partner...">
            @foreach($partners as $partner)
                <flux:select.option value="{{ $partner->id }}">{{ $partner->company }}</flux:select.option>
            @endforeach
        </flux:select>

        <div class="flex">
            <flux:spacer />
            @can('update', $resourceValue)
                <flux:button @click="$wire.attach(selectedPartnerId)" type="submit" variant="primary" size="sm">Attach partner</flux:button>
            @endcan
        </div>
    </flux:modal>
</div>
