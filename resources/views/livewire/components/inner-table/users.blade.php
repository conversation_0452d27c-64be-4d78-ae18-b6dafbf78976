<div>
    {{-- Users Listing --}}
    <div class="flex flex-col">
        <div class="flex justify-between">
            <div class="flex flex-col">
                <flux:heading size="lg" level="1">Users</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the users for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex items-center gap-2">
                @can('update', $resourceValue)
                    <flux:modal.trigger name="attach-user">
                        <flux:button size="sm">Attach user</flux:button>
                    </flux:modal.trigger>
                @endcan
            </div>
        </div>

        <div>
            @if ($this->users->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->users">
                    <flux:table.columns>
                        <flux:table.column>Relation</flux:table.column>
                        <flux:table.column>Name</flux:table.column>
                        <flux:table.column>Email</flux:table.column>
                        <flux:table.column>Phone</flux:table.column>
                        <flux:table.column>Role</flux:table.column>
                        <flux:table.column>Type</flux:table.column>
                        <flux:table.column>Employee</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->users as $user)
                            <flux:table.row :key="$user->id">
                                <flux:table.cell>
                                    @if($user->pivot->relation_type === 'creator')
                                        <flux:badge color="green" size="sm">{{ $user->pivot->relation_type }}</flux:badge>
                                    @elseif($user->pivot->relation_type === 'manual')
                                        <flux:badge color="blue" size="sm">{{ $user->pivot->relation_type }}</flux:badge>
                                    @elseif($user->pivot->relation_type === 'relational')
                                        <flux:badge color="gray" size="sm">{{ $user->pivot->relation_type }}</flux:badge>
                                    @else
                                        -
                                    @endif
                                </flux:table.cell>
                                <flux:table.cell>{{ $user->first_name }} {{ $user->last_name }}</flux:table.cell>
                                <flux:table.cell variant="strong">
                                    <a href="{{ route('users.show', $user->id) }}" wire:navigate>{{ $user->email }}</a>
                                </flux:table.cell>
                                <flux:table.cell>
                                    {{ $user->phone }}
                                </flux:table.cell>
                                <flux:table.cell>
                                    {{ $user->roles()->first()?->name ?? '-'  }}
                                </flux:table.cell>
                                <flux:table.cell>
                                    <flux:badge color="{{ $user->type->color() }}" size="sm" inset>{{ $user->type->label() }}</flux:badge>
                                </flux:table.cell>
                                <flux:table.cell>
                                    @if($user->is_employee)
                                        <flux:icon.check-circle size="sm" class="text-green-500 dark:text-green-300" />
                                    @else
                                        <flux:icon.x-circle size="sm" class="text-red-500 dark:text-red-300" />
                                    @endif
                                </flux:table.cell>
                                <flux:table.cell class="flex justify-end items-center gap-2">
                                    <flux:dropdown>
                                        <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                        <flux:menu>
                                            @can('view', $user)
                                                <flux:menu.item wire:click="view({{ $user->id }})" icon="eye">View</flux:menu.item>
                                            @endcan

                                            @if($user->pivot->relation_type === 'manual')
                                                @can('update', $resourceValue)
                                                    <flux:menu.separator />
                                                    <flux:menu.item wire:confirm="Are you sure you want to detach this user? This will not delete the user itself." wire:click="detach({{ $user->id }})" variant="danger" icon="x-mark">Detach</flux:menu.item>
                                                @endcan
                                            @endif
                                        </flux:menu>
                                    </flux:dropdown>
                                </flux:table.cell>
                            </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    {{-- Attach User - Modal --}}
    <flux:modal name="attach-user" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" @close="resetForm" :dismissible="false">
        <div>
            <flux:heading size="lg">Attach users</flux:heading>
            <flux:subheading>Attach users to the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <form wire:submit="attach" class="flex flex-col gap-6">
            <flux:select wire:model.live="selectedUserIds" variant="listbox" multiple searchable clearable placeholder="Choose users..." label="Users" description="This will be publicly displayed.">
                @foreach($selectableUsers as $user)
                    <flux:select.option value="{{ $user->id }}">{{ $user->first_name }} {{ $user->last_name }} - {{ $user->email }}</flux:select.option>
                @endforeach
            </flux:select>

            <div class="flex">
                <flux:spacer />
                @can('update', $resourceValue)
                    <flux:button type="submit" variant="primary" size="sm">Attach users</flux:button>
                @endcan
            </div>
        </form>
    </flux:modal>
</div>
