<div>
    {{-- Contacts Listing --}}
    <div class="flex flex-col mt-12">
        <div class="flex justify-between">
            <div class="flex flex-col">
                <flux:heading size="lg" level="1">Contacts</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the contacts for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex items-center gap-2">
                @can('update', $resourceValue)
                    <flux:modal.trigger name="attach-contact">
                        <flux:button size="sm">Attach contact</flux:button>
                    </flux:modal.trigger>
                @endcan
            </div>
        </div>

        <div>
            @if ($this->contacts->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->contacts">
                    <flux:table.columns>
                        <flux:table.column>Name</flux:table.column>
                        <flux:table.column>Departments</flux:table.column>
                        <flux:table.column>Email</flux:table.column>
                        <flux:table.column>Phone</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->contacts as $contact)
                        <flux:table.row :key="$contact->id">
                            <flux:table.cell variant="strong">
                                <a href="{{ route('contacts.show', $contact->id) }}" wire:navigate>{{ $contact->name }}</a>
                            </flux:table.cell>
                            <flux:table.cell>
                                @if($contact->departments)
                                    @foreach($contact->departments as $department)
                                        <span>{{ \App\Enums\ContactPositions::from($department)->label() }}</span>@if(!$loop->last), @endif
                                    @endforeach
                                @else
                                    -
                                @endif
                            </flux:table.cell>
                            <flux:table.cell>{{ $contact->email }}</flux:table.cell>
                            <flux:table.cell>{{ $contact->phone }}</flux:table.cell>

                            <flux:table.cell class="flex justify-end">
                                <flux:dropdown>
                                    <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                    <flux:menu>
                                        @can('view', $contact)
                                            <flux:menu.item wire:click="view({{ $contact->id }})" icon="eye">View</flux:menu.item>
                                        @endcan
                                        @can('update', $contact)
                                            <flux:menu.item wire:click="edit({{ $contact->id }})" icon="pencil-square">Edit</flux:menu.item>
                                        @endcan
                                        @can('delete', $contact)
                                            <flux:menu.separator />
                                            <flux:menu.item wire:confirm="Are you sure you want to detach this contact? This will not delete the contact itself." wire:click="detach({{ $contact->id }})" variant="danger" icon="x-mark">Detach</flux:menu.item>
                                        @endcan
                                    </flux:menu>
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    {{-- Attach Contact - Modal --}}
    <flux:modal name="attach-contact" variant="flyout" class="space-y-12" x-data="{ selectedContactId: null }" x-on:close="selectedContactId = null" :dismissible="false">
        <div>
            <flux:heading size="lg">Attach contact</flux:heading>
            <flux:subheading>Attach contact to the client.</flux:subheading>
        </div>

        <flux:select searchable x-model="selectedContactId" variant="listbox" searchable clearable placeholder="Choose contact...">
            @foreach($contacts as $contact)
                <flux:select.option value="{{ $contact->id }}">{{ $contact->name }}</flux:select.option>
            @endforeach
        </flux:select>

        <div class="flex">
            <flux:spacer />
            <flux:button @click="$wire.attach(selectedContactId)" type="submit" variant="primary" size="sm">Attach contact</flux:button>
        </div>
    </flux:modal>
</div>
