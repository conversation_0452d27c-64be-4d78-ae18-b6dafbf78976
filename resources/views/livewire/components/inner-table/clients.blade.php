<div>
    {{-- Clients Listing --}}
    <div class="flex flex-col">
        <div class="flex justify-between">
            <div class="flex flex-col">
                <flux:heading size="lg" level="1">Clients</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the clients for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex items-center gap-2">
                @can('update', $resourceValue)
                    <flux:modal.trigger name="attach-client">
                        <flux:button size="sm">Attach client</flux:button>
                    </flux:modal.trigger>
                @endcan
            </div>
        </div>

        <div>
            @if ($this->clients->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->clients">
                    <flux:table.columns>
                        <flux:table.column>Name</flux:table.column>
                        <flux:table.column>Type</flux:table.column>
                        <flux:table.column>Internal Ref.</flux:table.column>
                        <flux:table.column>Commercial Category</flux:table.column>
                        <flux:table.column>Priority</flux:table.column>
                        <flux:table.column>Partner</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->clients as $client)
                        <flux:table.row>
                            <flux:table.cell variant="strong">
                                <a href="{{ route('clients.show', $client->id) }}" wire:navigate>{{ $client->company }}</a>
                            </flux:table.cell>
                            <flux:table.cell><flux:badge icon="{{ $client->type->icon() }}" color="{{ $client->type->color() }}" size="sm" inset>{{ $client->type->label() }}</flux:badge></flux:table.cell>
                            <flux:table.cell>{{ $client->internalReferent ? ($client->internalReferent->first_name . ' ' . $client->internalReferent->last_name) : '-' }}</flux:table.cell>
                            <flux:table.cell>{{ $client->commercial_category ? $client->commercial_category->label() : '-' }}</flux:table.cell>
                            <flux:table.cell>{{ $client->priority ? $client->priority->label() : '-' }}</flux:table.cell>
                            <flux:table.cell variant="strong">
                                @if ($client->partner)
                                    <flux:button href="{{ route('partners.show', $client->partner->id ?? '') }}" wire:navigate size="xs" variant="ghost">{{ $client->partner->company ?? '' }}</flux:button>
                                @else
                                    -
                                @endif
                            </flux:table.cell>

                            <flux:table.cell class="flex justify-end">
                                <flux:dropdown>
                                    <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                    <flux:menu>
                                        @can('view', $client)
                                            <flux:menu.item wire:click="view({{ $client->id }})" icon="eye">View</flux:menu.item>
                                        @endcan
                                        @can('update', $client)
                                            <flux:menu.item wire:click="edit({{ $client->id }})" icon="pencil-square">Edit</flux:menu.item>
                                        @endcan
                                        @can('update', $resourceValue)
                                            <flux:menu.separator />
                                            <flux:menu.item wire:confirm="Are you sure you want to detach this client? This will not delete the client itself." wire:click="detach({{ $client->id }})" variant="danger" icon="x-mark">Detach</flux:menu.item>
                                        @endcan
                                    </flux:menu>
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    {{-- Attach Client - Modal --}}
    <flux:modal name="attach-client" variant="flyout" class="space-y-12" x-data="{ selectedClientId: null }" x-on:close="selectedClientId = null" :dismissible="false">
        <div>
            <flux:heading size="lg">Attach client</flux:heading>
            <flux:subheading>Attach client to the supplier.</flux:subheading>
        </div>

        <flux:select searchable x-model="selectedClientId" variant="listbox" searchable clearable placeholder="Choose client...">
            @foreach($clients as $client)
                <flux:select.option value="{{ $client->id }}">{{ $client->company }}</flux:select.option>
            @endforeach
        </flux:select>

        <div class="flex">
            <flux:spacer />
            @can('update', $resourceValue)
                <flux:button @click="$wire.attach(selectedClientId)" type="submit" variant="primary" size="sm">Attach client</flux:button>
            @endcan
        </div>
    </flux:modal>
</div>
