<div>
    {{-- Suppliers Listing --}}
    <div class="flex flex-col">
        <div class="flex justify-between">
            <div class="flex flex-col">
                <flux:heading size="lg" level="1">Suppliers</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the suppliers for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex items-center gap-2">
                @can('update', $resourceValue)
                    <flux:modal.trigger name="attach-supplier">
                        <flux:button size="sm">Attach supplier</flux:button>
                    </flux:modal.trigger>
                @endcan
            </div>
        </div>

        <div>
            @if ($this->suppliers->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->suppliers">
                    <flux:table.columns>
                        <flux:table.column>Code</flux:table.column>
                        <flux:table.column>Name</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->suppliers as $supplier)
                        <flux:table.row>
                            <flux:table.cell variant="strong">
                                <a href="{{ route('suppliers.show', $supplier->id) }}" wire:navigate>{{ $supplier->code }}</a>
                            </flux:table.cell>
                            <flux:table.cell>{{ $supplier->name }}</flux:table.cell>

                            <flux:table.cell class="flex justify-end">
                                <flux:dropdown>
                                    <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                    <flux:menu>
                                        @can('view', $supplier)
                                            <flux:menu.item wire:click="view({{ $supplier->id }})" icon="eye">View</flux:menu.item>
                                        @endcan
                                        @can('update', $supplier)
                                            <flux:menu.item wire:click="edit({{ $supplier->id }})" icon="pencil-square">Edit</flux:menu.item>
                                        @endcan
                                        @can('update', $resourceValue)
                                            <flux:menu.separator />
                                            <flux:menu.item wire:confirm="Are you sure you want to detach this supplier? This will not delete the supplier itself." wire:click="detach({{ $supplier->id }})" variant="danger" icon="x-mark">Detach</flux:menu.item>
                                        @endcan
                                    </flux:menu>
                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    {{-- Attach Supplier - Modal --}}
    <flux:modal name="attach-supplier" variant="flyout" class="space-y-12" x-data="{ selectedSupplierId: null }" x-on:close="selectedSupplierId = null" :dismissible="false">
        <div>
            <flux:heading size="lg">Attach supplier</flux:heading>
            <flux:subheading>Attach supplier to the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <flux:select searchable x-model="selectedSupplierId" variant="listbox" searchable clearable placeholder="Choose supplier..." label="Supplier" description="This will be publicly displayed.">
            @foreach($suppliers as $supplier)
                <flux:select.option value="{{ $supplier->id }}">{{ $supplier->name }}</flux:select.option>
            @endforeach
        </flux:select>

        <div class="flex">
            <flux:spacer />
            @can('update', $resourceValue)
                <flux:button @click="$wire.attach(selectedSupplierId)" type="submit" variant="primary" size="sm">Attach supplier</flux:button>
            @endcan
        </div>
    </flux:modal>
</div>
