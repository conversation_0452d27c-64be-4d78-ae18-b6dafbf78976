<?php

namespace App\Enums;

enum ModelTypes: string
{
    case Users = 'users';
    case Roles = 'roles';
    case Orders = 'orders';
    case Projects = 'projects';
    case Clients = 'clients';
    case Partners = 'partners';
    case Brands = 'brands';
    case Products = 'products';
    case DiscountGroups = 'discount_groups';
    case Suppliers = 'suppliers';
    case Collections = 'collections';
    case Contacts = 'contacts';
    
    public function label()
    {
        return match ($this) {
            static::Users => 'Users',
            static::Roles => 'Roles',
            static::Orders => 'Orders',
            static::Projects => 'Projects',
            static::Clients => 'Clients',
            static::Partners => 'Partners',
            static::Brands => 'Brands',
            static::Products => 'Products',
            static::DiscountGroups => 'Discount Groups',
            static::Suppliers => 'Suppliers',
            static::Collections => 'Collections',
            static::Contacts => 'Contacts',
        };
    }

    public static function mapPermissions(): array
    {
        return collect(static::cases())->mapWithKeys(function (ModelTypes $type) {
            return [$type->value => [
                'read' => false,
                'write' => false,
            ]];
        })->toArray();
    }
}
