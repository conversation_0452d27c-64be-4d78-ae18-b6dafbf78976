<?php

namespace App\Enums;

enum ExportSources: string
{
    case OrderSubmit = 'order_submit';
    case User = 'user';
    case Scheduler = 'scheduler';

    /**
     * Get the human-readable label for the export source
     */
    public function label(): string
    {
        return match ($this) {
            static::OrderSubmit => 'Order Submit',
            static::User => 'User Interface',
            static::Scheduler => 'Scheduled Task',
        };
    }

    /**
     * Get the icon for the export source
     */
    public function icon(): string
    {
        return match ($this) {
            static::OrderSubmit => 'paper-airplane',
            static::User => 'user',
            static::Scheduler => 'clock',
        };
    }

    /**
     * Get the color for the export source
     */
    public function color(): string
    {
        return match ($this) {
            static::OrderSubmit => 'blue',
            static::User => 'green',
            static::Scheduler => 'purple',
        };
    }
}
