<?php

namespace App\Livewire\Forms;

use Livewire\Attributes\Validate;
use Livewire\Form;
use App\Models\Partner;

class PartnerForm extends Form
{
    public $isEditMode = false;

    public ?Partner $partner;
    public $internal_referent_id;
    public $area_manager_id;
    public $payment_term_id;

    public $type = '';
    public $company = '';
    public $email = '';
    public $country = NULL;
    public $commercial_category = NULL;
    public $priority;
    public $minimum_orderable;
    public $handling_and_packing;
    public $delivery_terms;
    public $countries_of_expertise = [];
    public $notes;

    public function rules()
    {
        // Trim email before validation
        if ($this->email) {
            $this->email = trim($this->email);
        }
        
        return [
            'internal_referent_id' => 'nullable',
            'area_manager_id' => 'nullable',
            'payment_term_id' => 'nullable',

            'type' => 'required',
            'company' => 'required|min:3',
            'email' => 'required|email',
            'country' => 'nullable|min:3',
            'commercial_category' => 'nullable',
            'priority' => 'nullable',
            'minimum_orderable' => 'nullable',
            'handling_and_packing' => 'nullable|min:0|max:100|numeric',
            'delivery_terms' => 'nullable',
            'countries_of_expertise' => 'nullable|array',
            'notes' => 'nullable',
        ];
    }

    public function setPartner(Partner $partner)
    {
        $this->isEditMode = true;
        $this->partner = $partner;

        $this->internal_referent_id = $partner->internalReferent->id ?? NULL;
        $this->area_manager_id = $partner->areaManager->id ?? NULL;
        $this->payment_term_id = $partner->paymentTerm->id ?? NULL;

        $this->type = $partner->type;
        $this->company = $partner->company;
        $this->email = $partner->email;
        $this->country = $partner->country->value ?? NULL;
        $this->commercial_category = $partner->commercial_category ?? NULL;
        $this->priority = $partner->priority;
        $this->minimum_orderable = $partner->minimum_orderable;
        $this->handling_and_packing = $partner->handling_and_packing;
        $this->delivery_terms = $partner->delivery_terms;
        $this->countries_of_expertise = $partner->countries_of_expertise ?? [];
        $this->notes = $partner->notes;
    }

    public function store()
    {
        $this->validate();

        $partner = new Partner();
        $partner->internal_referent_id = $this->internal_referent_id ?: NULL;
        $partner->area_manager_id = $this->area_manager_id ?: NULL;
        $partner->payment_term_id = $this->payment_term_id ?: NULL;

        $partner->type = $this->type;
        $partner->company = $this->company;
        $partner->email = $this->email;
        $partner->country = $this->country;
        $partner->commercial_category = $this->commercial_category;
        $partner->priority = $this->priority;
        $partner->minimumOrderable = $this->minimum_orderable ?: NULL;
        $partner->handling_and_packing = $this->handling_and_packing;
        $partner->delivery_terms = $this->delivery_terms;
        $partner->countries_of_expertise = $this->countries_of_expertise;
        $partner->notes = $this->notes;
        $partner->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->partner->internal_referent_id = $this->internal_referent_id ?: NULL;
        $this->partner->area_manager_id = $this->area_manager_id ?: NULL;
        $this->partner->payment_term_id = $this->payment_term_id ?: NULL;

         $this->partner->type = $this->type;
        $this->partner->company = $this->company;
        $this->partner->email = $this->email;
        $this->partner->country = $this->country;
        $this->partner->commercial_category = $this->commercial_category;
        $this->partner->priority = $this->priority;
        $this->partner->minimumOrderable = $this->minimum_orderable ?: NULL;
        $this->partner->handling_and_packing = $this->handling_and_packing;
        $this->partner->delivery_terms = $this->delivery_terms;
        $this->partner->countries_of_expertise = $this->countries_of_expertise;
        $this->partner->notes = $this->notes;
        $this->partner->save();

        $this->reset();
    }
}
