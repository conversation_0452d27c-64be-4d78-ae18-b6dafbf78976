<?php

namespace App\Livewire\Forms;

use Livewire\Form;
use App\Models\Client;

class ClientForm extends Form
{
    public $isEditMode = false;

    public ?Client $client;
    public $partner_id;
    public $internal_referent_id;
    public $area_manager_id;
    public $payment_term_id;

    public $type = '';
    public $company = '';
    public $email = '';
    public $country = NULL;
    public $commercial_category = NULL;
    public $priority;
    public $minimum_orderable;
    public $handling_and_packing;
    public $delivery_terms;
    public $countries_of_expertise = [];
    public $notes;

    public function rules()
    {
        // Trim email before validation
        if ($this->email) {
            $this->email = trim($this->email);
        }
        
        return [
            'partner_id' => 'nullable',
            'internal_referent_id' => 'nullable',
            'area_manager_id' => 'nullable',
            'payment_term_id' => 'nullable',

            'type' => 'required',
            'company' => 'required|min:3',
            'email' => 'required|email',
            'country' => 'nullable|min:3',
            'commercial_category' => 'nullable',
            'priority' => 'nullable',
            'minimum_orderable' => 'nullable|numeric',
            'handling_and_packing' => 'nullable|min:0|max:100|numeric',
            'delivery_terms' => 'nullable',
            'countries_of_expertise' => 'nullable|array',
            'notes' => 'nullable',
        ];
    }

    public function setClient(Client $client)
    {
        $this->isEditMode = true;
        $this->client = $client;

        $this->partner_id = $client->partner->id ?? NULL;
        $this->internal_referent_id = $client->internalReferent->id ?? NULL;
        $this->area_manager_id = $client->areaManager->id ?? NULL;
        $this->payment_term_id = $client->paymentTerm->id ?? NULL;

        $this->type = $client->type;
        $this->company = $client->company;
        $this->email = $client->email;
        $this->country = $client->country->value ?? NULL;
        $this->commercial_category = $client->commercial_category ?? NULL;
        $this->priority = $client->priority;
        $this->minimum_orderable = $client->minimum_orderable;
        $this->handling_and_packing = $client->handling_and_packing;
        $this->delivery_terms = $client->delivery_terms;
        $this->countries_of_expertise = $client->countries_of_expertise ?? [];
        $this->notes = $client->notes;
    }

    public function store()
    {
        $this->validate();

        $client = new Client();
        $client->partner_id = $this->partner_id ?: NULL;
        $client->internal_referent_id = $this->internal_referent_id ?: NULL;
        $client->area_manager_id = $this->area_manager_id ?: NULL;
        $client->payment_term_id = $this->payment_term_id ?: NULL;

        $client->type = $this->type;
        $client->company = $this->company;
        $client->email = $this->email;
        $client->country = $this->country;
        $client->commercial_category = $this->commercial_category;
        $client->priority = $this->priority;
        $client->minimumOrderable = $this->minimum_orderable ?: NULL;
        $client->handling_and_packing = $this->handling_and_packing;
        $client->delivery_terms = $this->delivery_terms;
        $client->countries_of_expertise = $this->countries_of_expertise;
        $client->notes = $this->notes;
        $client->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->client->partner_id = $this->partner_id ?: NULL;
        $this->client->internal_referent_id = $this->internal_referent_id ?: NULL;
        $this->client->area_manager_id = $this->area_manager_id ?: NULL;
        $this->client->payment_term_id = $this->payment_term_id ?: NULL;

        $this->client->type = $this->type;
        $this->client->company = $this->company;
        $this->client->email = $this->email;
        $this->client->country = $this->country ?: NULL;
        $this->client->commercial_category = $this->commercial_category;
        $this->client->priority = $this->priority;
        $this->client->minimumOrderable = $this->minimum_orderable ?: NULL;
        $this->client->handling_and_packing = $this->handling_and_packing;
        $this->client->delivery_terms = $this->delivery_terms;
        $this->client->countries_of_expertise = $this->countries_of_expertise;
        $this->client->notes = $this->notes;
        $this->client->save();

        $this->reset();
    }
}
