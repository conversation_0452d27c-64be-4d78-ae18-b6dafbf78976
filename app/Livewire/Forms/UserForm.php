<?php

namespace App\Livewire\Forms;

use Livewire\Form;
use App\Models\User;
use Livewire\Attributes\Validate;
use Spatie\Permission\Models\Role;

class UserForm extends Form
{
    public $isEditMode = false;

    public ?User $user;

    public $first_name = '';
    public $last_name = '';
    public $phone = '';
    public $is_employee = false;
    public $type = '';
    public $role = '';
    public $email = '';
    public $password;
    public $password_confirmation;

    protected function rules()
    {
        // Trim email before validation
        if ($this->email) {
            $this->email = trim($this->email);
        }

        $rules = [
            'first_name' => 'required|min:3',
            'last_name' => 'required|min:3',
            'phone' => 'nullable',
            'is_employee' => 'required',
            'type' => 'required',
            'role' => 'nullable',
            'email' => 'required|email',
        ];

        if ($this->isEditMode) {
            $rules['password'] = 'nullable|min:8|confirmed';
        } else {
            $rules['password'] = 'required|min:8|confirmed';
        }

        return $rules;
    }

    public function setUser(User $user)
    {
        $this->isEditMode = true;
        $this->user = $user;

        $this->first_name = $user->first_name;
        $this->last_name = $user->last_name;
        $this->phone = $user->phone;
        $this->is_employee = $user->is_employee;
        $this->type = $user->isAdmin() ? 'admin' : 'member';
        $this->email = $user->email;

        $this->role = $user->roles()->first()->name ?? '';
    }

    public function store()
    {
        $this->validate();

        $user = new User();
        $user->first_name = $this->first_name;
        $user->last_name = $this->last_name;
        $user->phone = $this->phone;
        $user->is_employee = $this->is_employee;
        $user->is_admin = $this->type === 'admin';
        $user->email = $this->email;
        $user->password = $this->password;

        $user->save();

        $this->assignRole($user);

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->user->first_name = $this->first_name;
        $this->user->last_name = $this->last_name;
        $this->user->phone = $this->phone;
        $this->user->is_employee = $this->is_employee;
        $this->user->is_admin = $this->type === 'admin';
        $this->user->email = $this->email;
        $this->user->password = $this->password ?? $this->user->password;

        $this->user->save();

        $this->assignRole($this->user);

        $this->reset();

    }

    public function assignRole($user){
        if ($this->role) {
            $user->syncRoles([$this->role]);
        } else {
            $user->syncRoles([]);
        }
    }
}
