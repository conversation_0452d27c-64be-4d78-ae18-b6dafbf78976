<?php

namespace App\Livewire\Forms;

use Livewire\Form;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleForm extends Form
{
    public $isEditMode = false;

    public ?Role $role;
    public $name = '';
    public $permissions = [];

    public function rules()
    {
        return [
            'name' => 'required|string|min:3|lowercase|unique:roles,name,' . ($this->role->id ?? 'NULL') . '|regex:/^[a-z0-9-_]+$/',
        ];
    }

    public function store()
    {
        $this->validate();

        // Create a new role
        $role = Role::create(['name' => $this->name]);

        // Assign permissions to the new role
        $this->assignPermissions($role);

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->role->name = $this->name;
        $this->role->save();

        // Remove all existing permissions from the role
        $this->role->permissions()->detach();

        // Assign permissions to the new role
        $this->assignPermissions($this->role);

        $this->reset();
    }

    private function assignPermissions(Role $role)
    {
        foreach ($this->permissions as $resource => $actions) {
            foreach ($actions as $action => $isEnabled) {
                if ($isEnabled) {
                    // Create or find the permission
                    $permissionName = "{$action}_{$resource}";
                    $permission = Permission::firstOrCreate(['name' => $permissionName]);

                    // Assign the permission to the role
                    $role->givePermissionTo($permission);
                }
            }
        }
    }

    public function setRole(Role $role)
    {
        $this->isEditMode = true;
        $this->role = $role;
        $this->name = $role->name;

        // Initialize permissions
        $permissions = $role->permissions->pluck('name')->toArray();

        collect($this->permissions)->keys()->each(function ($resource) use ($permissions) {
            $this->permissions[$resource]['read'] = in_array("read_{$resource}", $permissions);
            $this->permissions[$resource]['write'] = in_array("write_{$resource}", $permissions);
        });

    }
}
