<?php

namespace App\Livewire\Components\Orders\Creator\Modals;

use Flux\Flux;
use App\Models\Product;
use Livewire\Component;
use App\Enums\AddonCodes;
use App\Models\Order\Order;
use Livewire\Attributes\On;
use App\Models\Order\OrderRow;
use Illuminate\Support\Facades\DB;
use App\Livewire\Forms\CartGroupForm;
use App\Livewire\Forms\OrderAddonForm;
use App\Livewire\Forms\CustomProductForm;

class Cart extends Component
{
    public Order $order;
    public Product $product;
    
    public $selectedCartGroup;

    public CartGroupForm $createCartGroupForm;
    public CartGroupForm $editCartGroupForm;

    public CustomProductForm $createCustomProductForm;

    public OrderAddonForm $createAddonForm;
    public OrderAddonForm $editAddonForm;

    public function render()
    {
        return view('livewire.components.orders.creator.modals.cart', 
            [
                'addonCodes' => AddonCodes::cases(),
            ]
        );
    }

    public function sort($item, $position)
    {
        $row = OrderRow::findOrFail($item);

        $this->moveOrderRow($row, $position);
    }

    #[On('order-updated')]
    public function refreshOrder()
    {
        $this->order->refresh();
    }

    #[On('cart-closed')]
    public function refreshAll()
    {
        // Refresh all data for the order
        $this->dispatch('order-updated');
        $this->dispatch('row-updated');
    }

    /**
     * Show the cart modal.
     */
    #[On('show-cart')]
    public function show()
    {
        $this->order->load([
            'cartGroups',
            'cartGroups.orderRows',
            'cartGroups.orderRows.product',
            'cartGroups.orderRows.product.brand',
            'cartGroups.orderRows.product.collection',
            'cartGroups.orderRows.product.modules',
            'cartGroups.orderRows.customProduct'
        ]);
        
        Flux::modal('cart')->show();
    }

    #[On('cart-group-content-updated')]
    public function refreshCartGroups()
    {
        $this->order->load([
            'cartGroups',
            'cartGroups.orderRows',
            'cartGroups.orderRows.product',
            'cartGroups.orderRows.product.brand',
            'cartGroups.orderRows.product.collection',
            'cartGroups.orderRows.product.modules',
            'cartGroups.orderRows.customProduct'
        ]);
    }

    /**
     * Add a product to the order.
     */
    #[On('add-to-order')]
    public function addToOrder(int $productId, int $quantity, array $options): void
    {
        // Get the product if exists
        $this->product = Product::findOrFail($productId);

        // If product does not exist, return an error
        if (!$this->product) {
            Flux::toast(
                variant: 'danger',
                text: 'The PRODUCT does not exist.',
            );
        }

        // Check if selectedCartGroup is set (true -> go to next step, false -> create a new cart group)
        if (!$this->selectedCartGroup) {
            // Check if a cart group exists for the order (true -> select the cart group, false -> create a new cart group)
            if ($this->order->cartGroups->count() > 0) {
                // Select the first cart group
                $this->selectedCartGroup = $this->order->cartGroups->first();
            } else {
                // Create a new cart group
                $this->selectedCartGroup = $this->order->cartGroups()->create([
                    'sort' => $this->order->cartGroups()->count() === 0 ? 0 : $this->order->cartGroups()->max('sort') + 1,
                    'name' => 'Default',
                ]);
            }
        }

        // Check if the product is already in the selected cart group (true -> add quantity, false -> create new order row)
        if ($this->product->modules->isNotEmpty()) {
            $existingProduct = $this->selectedCartGroup->orderRows
                ->first(fn ($row) => $row->product_id === $this->product->id && $row->options === $options);

            if ($existingProduct) {
                $existingProduct->increment('quantity', $quantity);
            } else {
                $this->selectedCartGroup->orderRows()->create([
                    'sort' => $this->selectedCartGroup->orderRows()->count() === 0 ? 0 : $this->selectedCartGroup->orderRows()->max('sort') + 1,
                    'product_id' => $this->product->id,
                    'quantity' => $quantity,
                    'options' => $options,
                ]);
            }
        } else {
            if ($this->selectedCartGroup->products->contains($this->product)) {
                $this->selectedCartGroup->products()->updateExistingPivot($this->product, [
                    'quantity' => $this->selectedCartGroup->products->find($this->product)->pivot->quantity + $quantity,
                ]);
            } else {
                $this->selectedCartGroup->products()->attach($this->product, [
                    'sort' => $this->selectedCartGroup->orderRows()->count() === 0 ? 0 : $this->selectedCartGroup->orderRows()->max('sort') + 1,
                    'quantity' => $quantity,
                ]);
            }
        }
        
        Flux::toast(
            variant: 'success',
            text: 'The PRODUCT has been added.'
        );

        // Dispatch event to the main component to update the order
        $this->dispatch('order-updated');
        $this->dispatch('row-updated');
    }

    /**
     * Select a cart group.
     */
    public function selectCartGroup(int $cartGroupId): void
    {
        $this->selectedCartGroup = $this->order->cartGroups->find($cartGroupId);
    }

    /**
     * Save a cart group.
     */
    public function saveCartGroup(): void
    {
        // Set the order id and sort
        $this->createCartGroupForm->order_id = $this->order->id;
        $this->createCartGroupForm->sort = $this->order->cartGroups()->count() === 0 ? 0 : $this->order->cartGroups()->max('sort') + 1;

        // Store the cart group
        $this->createCartGroupForm->store();

        // Select the new cart group
        $this->selectedCartGroup = $this->order->cartGroups->last();

        Flux::toast(
            variant: 'success',
            text: 'The CART GROUP has been saved.'
        );
    }

    /**
     * Edit a cart group.
     */
    public function editCartGroup(int $cartGroupId): void
    {
        $this->editCartGroupForm->reset();
        $this->resetValidation();

        $cartGroup = $this->order->cartGroups->find($cartGroupId);
        
        $this->editCartGroupForm->setCartGroup($cartGroup);
    }

    /**
     * Cancel editing a cart group.
     */
    public function cancelEditCartGroup(): void
    {
        $this->editCartGroupForm->reset();
        $this->resetValidation();
    }

    /**
     * Update a cart group.
     */
    public function updateCartGroup(): void
    {
        $this->editCartGroupForm->update();

        Flux::toast(
            variant: 'success',
            text: 'The CART GROUP has been updated.'
        );
    }

    /**
     * Delete a cart group.
     */
    public function deleteCartGroup(int $cartGroupId): void
    {
        $cartGroup = $this->order->cartGroups->find($cartGroupId);

        $cartGroup->delete();

        $this->selectedCartGroup = NULL;
        $this->dispatch('cart-group-content-updated');

        Flux::toast(
            variant: 'success',
            text: 'The CART GROUP has been deleted.'
        );
    }

    /**
     * Save a custom product.
     */
    public function saveCustomProduct(): void
    {
        // Set the user id
        $this->createCustomProductForm->user_id = auth()->user()->id ?? null;

        // Store the custom product
        $customProduct = $this->createCustomProductForm->store();

        // Check if selectedCartGroup is set (true -> go to next step, false -> create a new cart group)
        if (!$this->selectedCartGroup) {
            // Check if a cart group exists for the order (true -> select the cart group, false -> create a new cart group)
            if ($this->order->cartGroups->count() > 0) {
                // Select the first cart group
                $this->selectedCartGroup = $this->order->cartGroups->first();
            } else {
                // Create a new cart group
                $this->selectedCartGroup = $this->order->cartGroups()->create([
                    'sort' => $this->order->cartGroups()->count() === 0 ? 0 : $this->order->cartGroups()->max('sort') + 1,
                    'name' => 'Default',
                ]);
            }
        }

        // Create a new order row
        $this->selectedCartGroup->orderRows()->create([
            'sort' => $this->selectedCartGroup->orderRows()->count() === 0 ? 0 : $this->selectedCartGroup->orderRows()->max('sort') + 1,
            'custom_product_id' => $customProduct->id,
            'quantity' => 1,
        ]);

        $this->dispatch('order-updated');

        Flux::toast(
            variant: 'success',
            text: 'The CUSTOM PRODUCT has been saved.'
        );
    }

    /**
     * Save an addon.
     */
    public function saveAddon(): void
    {
        // Set the order id
        $this->createAddonForm->order_id = $this->order->id;

        // Store the addon
        $this->createAddonForm->store();

        Flux::toast(
            variant: 'success',
            text: 'The ADDON has been saved.'
        );

        $this->dispatch('order-updated');
    }

    /**
     * Edit an addon.
     */
    public function editAddon(int $addonId): void
    {
        $this->editAddonForm->reset();
        $this->resetValidation();

        $addon = $this->order->addons->find($addonId);
        
        $this->editAddonForm->setOrderAddon($addon);
    }

    /**
     * Cancel editing an addon.
     */
    public function cancelEditAddon(): void
    {
        $this->editAddonForm->reset();
        $this->resetValidation();
    }

    /**
     * Update an addon.
     */
    public function updateAddon(): void
    {
        $this->editAddonForm->update();

        Flux::toast(
            variant: 'success',
            text: 'The ADDON has been updated.'
        );

        $this->dispatch('order-updated');
    }

    /**
     * Delete an addon.
     */
    public function deleteAddon(int $addonId): void
    {
        $addon = $this->order->addons->find($addonId);

        $addon->delete();

        Flux::toast(
            variant: 'success',
            text: 'The ADDON has been deleted.'
        );

        $this->dispatch('order-updated');
    }

    public function updatedCreateAddonFormCode($value)
    {
        $this->createAddonForm->description = AddonCodes::from($value)->label();
    }

    protected function moveOrderRow($row, $position)
    {
        DB::transaction(function () use ($row,$position) {
            $current = $row->sort;
            $after = $position;

            if ($current === $after) return;

            $row->update(['sort' => -1]);

            $block = $this->order->cartGroups()->find($row->cart_group_id)->orderRows()->whereBetween('sort', [
                min($current, $after),
                max($current, $after),
            ]);

            $needToShiftBlockUpBecauseDraggingTargetDown = $current < $after;

            $needToShiftBlockUpBecauseDraggingTargetDown
                ? $block->decrement('sort')
                : $block->increment('sort');

            $row->update(['sort' => $after]);
        });
    }
}
