<?php

namespace App\Livewire\Components\InnerTable;

use App\Enums\AddressTypes;
use Flux\Flux;
use App\Models\Address;
use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\WithPagination;
use Livewire\Attributes\Lazy;
use Livewire\Attributes\Computed;
use App\Livewire\Forms\AddressForm;
use PrinsFrank\Standards\Country\CountryAlpha3;

#[Lazy]
class Addresses extends Component
{
    use WithPagination;

    public ?AddressForm $form;
    
    public $resourceType;
    public $resourceNameSingular;
    public $resourceNamePlural;
    public $resourceValue;

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.inner-table.addresses', [
            'addressTypes' => AddressTypes::cases(),
            'invoicingAddresses' => $this->resourceValue->addresses()->where('type', AddressTypes::Invoicing)->get(),
            'countries' => CountryAlpha3::cases(),
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    public function updatedFormType(): void
    {
        $this->form->parent_id = null;
        $this->form->code_invoicing = null;
        $this->form->code_shipping = null;
        $this->form->company = null;
        $this->form->vat_number = null;
        $this->form->fiscal_code = null;
        $this->form->sdi_code = null;
        $this->form->street = null;
        $this->form->city = null;
        $this->form->state = null;
        $this->form->zip = null;
        $this->form->country = null;
    }

    #[Computed]
    public function addresses()
    {
        return $this->resourceValue->addresses()->orderBy('id', 'desc')->paginate(5, pageName: 'addresses-page');
    }

    public function view($id): void
    {
        $address = Address::findOrFail($id);

        $this->form->setAddress($address);

        Flux::modal('view-address')->show();
    }

    public function create(): void
    {
        $this->form->addressable_id = $this->resourceValue->id;
        $this->form->addressable_type = "App\\Models\\" . $this->resourceType;
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The ADDRESS has been created.'
        );
        
        Flux::modals()->close();
        $this->resetPage();
    }

    public function edit($id): void
    {
        $address = Address::findOrFail($id);

        // Check if the address has orders with status different from OPEN
        $nonOpenOrders = $address->invoicingOrders()->where('status', '!=', 'open')->count() +
                        $address->shippingOrders()->where('status', '!=', 'open')->count();

        if ($nonOpenOrders > 0) {
            Flux::toast(
                variant: 'danger',
                text: 'You cannot edit this ADDRESS because it is associated to one or more orders with status different from OPEN.'
            );
            return;
        }

        $this->form->setAddress($address);

        Flux::modal('edit-address')->show();
    }

    public function update(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The ADDRESS has been updated.'
        );
        
        Flux::modals()->close();
        $this->resetPage();
    }

    public function delete($id): void
    {
        $address = Address::findOrFail($id);

        // Check if the address is associated to any Order
        if ($address->invoicingOrders()->count() > 0 || $address->shippingOrders()->count() > 0) {
            Flux::toast(
                variant: 'danger',
                text: 'You cannot delete this ADDRESS because it is associated to one or more orders.'
            );
        }
        else {
            $address->delete();

            Flux::toast(
                variant: 'success',
                text: 'The ADDRESS has been deleted.'
            );
        }
    }

    public function resetForm(): void
    {
        $this->form->reset();
        $this->resetValidation();
    }
}
