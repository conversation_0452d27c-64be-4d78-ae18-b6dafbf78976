<?php

namespace App\Livewire\Components\InnerTable;

use Flux\Flux;
use App\Models\Contact;
use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\WithPagination;
use Livewire\Attributes\Lazy;
use Livewire\Attributes\Computed;

#[Lazy]
class Contacts extends Component
{
    use WithPagination;

    public $resourceType;
    public $resourceNameSingular;
    public $resourceNamePlural;
    public $resourceValue;

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }
    
    public function render()
    {
        return view('livewire.components.inner-table.contacts', [
            'contacts' => Contact::whereDoesntHave($this->resourceNamePlural, function ($query) {
                    $query->where($this->resourceNameSingular . '_id', $this->resourceValue->id);
                })->select('id', 'name')->get(),
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[Computed]
    public function contacts()
    {
        return $this->resourceValue->contacts()->orderBy('id', 'desc')->select('contact_id', 'name', 'email', 'phone', 'departments')->paginate(5, pageName: 'contacts-page');
    }

    public function attach($id): void
    {
        $this->resourceValue->contacts()->attach($id);

        Flux::toast(
            variant: 'success',
            text: 'The CONTACT has been attached.'
        );

        Flux::modals()->close();
        $this->resetPage();
    }

    public function detach($id): void
    {
        $this->resourceValue->contacts()->detach($id);

        Flux::toast(
            variant: 'success',
            text: 'The CONTACT has been detached.'
        );

        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('contacts.show', ['contact' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('contacts.edit', ['contact' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Contact::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The CONTACT has been deleted.'
        );
    }
}
