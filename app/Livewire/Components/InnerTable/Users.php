<?php

namespace App\Livewire\Components\InnerTable;

use App\Actions\Clients\Relations\AttachUsers as AttachUsersToClient;
use App\Actions\Partners\Relations\AttachUsers as AttachUsersToPartner;
use App\Actions\Clients\Relations\DetachUser as DetachUserFromClient;
use App\Actions\Partners\Relations\DetachUser as DetachUserFromPartner;
use App\Models\User;
use Flux\Flux;
use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\WithPagination;
use Livewire\Attributes\Lazy;
use Livewire\Attributes\Computed;

#[Lazy]
class Users extends Component
{
    use WithPagination;

    public $resourceType;
    public $resourceNameSingular;
    public $resourceNamePlural;
    public $resourceValue;

    public $selectableUsers = [];
    public $selectedUserIds = [];

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        $this->fetchSelectableUsers();

        return view('livewire.components.inner-table.users', [
            'users' => $this->users,
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[Computed]
    public function users()
    {
        return $this->resourceValue->users()->orderBy('id', 'desc')->paginate(5, pageName: 'client-users-page');
    }

    public function fetchSelectableUsers()
    {
        $this->selectableUsers =
            User::where('id', '!=', auth()->id())
                ->whereNotIn('id', $this->resourceValue->users->pluck('id'))
                ->get();
    }

    public function resetForm(): void
    {
        $this->selectableUsers = [];
        $this->selectedUserIds = [];
        $this->resetValidation();
    }

    public function attach(): void
    {
        // Validate
        $rules = [
            'selectedUserIds' => 'required|array|min:1',
            'selectedUserIds.*' => 'exists:users,id',
        ];
        $messages = [
            'selectedUserIds.required' => 'Please select at least one user to attach.',
            'selectedUserIds.*.exists' => 'The selected user does not exist.',
        ];
        $this->validate($rules, $messages);

        // Attach Users as manual relation type
        $existingUserIds = $this->resourceValue->users()->pluck('user_id')->toArray();
        $newUserIds = array_diff($this->selectedUserIds, $existingUserIds);
        $attachData = array_fill_keys($newUserIds, ['relation_type' => 'manual']);

        if (!empty($attachData)) {
            $this->resourceValue->users()->attach($attachData);

            if($this->resourceType === 'Client') {
                // If the resource type is Client, also attach the users to projects and orders with the same current client
                AttachUsersToClient::run($newUserIds, $this->resourceValue);
            } else if ($this->resourceType === 'Partner') {
                // If the resource type is Partner, retrive the partner's clients and attach the users to order and projects of each client
                AttachUsersToPartner::run($newUserIds, $this->resourceValue);
            }
        }


        Flux::toast(
            variant: 'success',
            text: 'USERs have been attached.'
        );

        Flux::modals()->close();

        $this->resetPage();
        $this->resetForm();
    }

    public function detach($id): void
    {
        $user = User::findOrFail($id);
        $this->resourceValue->users()->detach($id);

        if($this->resourceType === 'Client') {
            // If the resource type is Client, also detach the users from projects and orders with the same current client
            DetachUserFromClient::run($id, $this->resourceValue);
        } else if ($this->resourceType === 'Partner') {
            // If the resource type is Partner, retrive the partner's clients and detach the users from order and projects of each client
            DetachUserFromPartner::run($id, $this->resourceValue);
        }

        Flux::toast(
            variant: 'success',
            text: 'The USER has been detached.'
        );

        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('users.show', ['user' => $id], navigate: true);
    }
}
