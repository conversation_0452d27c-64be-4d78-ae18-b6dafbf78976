<?php

namespace App\Livewire\Components\InnerTable;

use Flux\Flux;
use Livewire\Component;
use Illuminate\Support\Str;
use App\Models\Collaborator;
use Livewire\WithPagination;
use Livewire\Attributes\Lazy;
use Livewire\Attributes\Computed;
use App\Livewire\Forms\CollaboratorForm;
use App\Models\Contact;
use App\Models\User;

#[Lazy]
class Collaborators extends Component
{
    use WithPagination;

    public ?CollaboratorForm $form;
    
    public $resourceType;
    public $resourceNameSingular;
    public $resourceNamePlural;
    public $resourceValue;

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.inner-table.collaborators', [
            'users' => User::all(),
            'contacts' => $this->resourceValue->client ? $this->resourceValue->client->contacts : Contact::all(),
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[Computed]
    public function collaborators()
    {
        return $this->resourceValue->collaborators()->orderBy('id', 'desc')->paginate(5, pageName: 'collaborators-page');
    }

    public function view($id): void
    {
        $collaborator = Collaborator::findOrFail($id);

        $this->form->setCollaborator($collaborator);

        Flux::modal('view-collaborator')->show();
    }

    public function create(): void
    {
        $this->form->collaboratable_id = $this->resourceValue->id;
        $this->form->collaboratable_type = "App\\Models\\" . $this->resourceType . "\\" . $this->resourceType;
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The COLLABORATOR has been created.'
        );
        
        Flux::modals()->close();
        $this->resetPage();
    }

    public function edit($id): void
    {
        $collaborator = Collaborator::findOrFail($id);

        $this->form->setCollaborator($collaborator);

        Flux::modal('edit-collaborator')->show();
    }

    public function update(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The COLLABORATOR has been updated.'
        );
        
        Flux::modals()->close();
        $this->resetPage();
    }

    public function delete($id): void
    {
        Collaborator::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The COLLABORATOR has been deleted.'
        );
    }

    public function resetForm(): void
    {
        $this->form->reset();
        $this->resetValidation();
    }
}
