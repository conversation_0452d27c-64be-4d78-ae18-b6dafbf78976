<?php

namespace App\Livewire\Components\InnerTable;

use Flux\Flux;
use App\Models\Asset;
use Livewire\Component;
use App\Enums\AssetTypes;
use Illuminate\Support\Str;
use App\Enums\AssetStatuses;
use Livewire\WithPagination;
use Livewire\Attributes\Lazy;
use Livewire\WithFileUploads;
use App\Livewire\Forms\AssetForm;
use Livewire\Attributes\Computed;
use Illuminate\Support\Facades\Storage;

#[Lazy]
class Moodboards extends Component
{
    use WithPagination, WithFileUploads;

    public ?AssetForm $form;

    public $resourceType;
    public $resourceNameSingular;
    public $resourceNamePlural;
    public $resourceValue;

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }
    
    public function render()
    {
        return view('livewire.components.inner-table.moodboards', [
            'assetTypes' => AssetTypes::cases(),
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[Computed]
    public function assets()
    {
        return $this->resourceValue->assets()
            ->where('type', AssetTypes::Moodboard)
            ->orderBy('id', 'desc')
            ->paginate(5, pageName: 'moodboards-page');
    }

    public function view($id): void
    {
        $asset = Asset::findOrFail($id);

        $this->form->setAsset($asset);

        Flux::modal('view-moodboard')->show();
    }

    public function create(): void
    {
        $this->form->assetable_id = $this->resourceValue->id;
        $this->form->assetable_type = "App\\Models\\" . $this->resourceType . "\\" . $this->resourceType;
        
        $this->form->type = AssetTypes::Moodboard;
        $this->form->status = AssetStatuses::Open;
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The MOODBOARD has been created.'
        );
        
        Flux::modals()->close();
        $this->resetPage();
    }

    public function edit($id): void
    {
        $asset = Asset::findOrFail($id);

        $this->form->setAsset($asset);

        Flux::modal('edit-moodboard')->show();
    }

    public function update(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The MOODBOARD has been updated.'
        );
        
        Flux::modals()->close();
        $this->resetPage();
    }

    public function delete($id): void
    {
        Asset::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The MOODBOARD has been deleted.'
        );
    }

    public function resetForm(): void
    {
        $this->form->reset();
        $this->resetValidation();
    }

    public function unsetFile(): void
    {
        $this->form->file = null;
    }

    public function downloadFile(Asset $asset)
    {
        $file = $asset->file;

        if (!Storage::disk(config('filesystems.private'))->exists($file ?? ' ')) {

            Flux::toast(
                variant: 'danger',
                text: 'The MOODBOARD does not exist.'
            );

            return false;
        }

        $url = Storage::disk(config('filesystems.private'))
            ->temporaryUrl($file, now()->addMinutes(5));

        return response()->streamDownload(function () use ($url) {
            echo file_get_contents($url);
        }, basename($file));
    }
}
