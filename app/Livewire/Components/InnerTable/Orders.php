<?php

namespace App\Livewire\Components\InnerTable;

use Flux\Flux;
use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\WithPagination;
use Livewire\Attributes\Lazy;
use Livewire\Attributes\Computed;

#[Lazy]
class Orders extends Component
{
    use WithPagination;

    public $resourceType;
    public $resourceNameSingular;
    public $resourceNamePlural;
    public $resourceValue;

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.inner-table.orders');
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[Computed]
    public function orders()
    {
        return $this->resourceValue->orders()->orderBy('id', 'desc')->paginate(5, pageName: 'orders-page');
    }

    public function view($id): void
    {
        $this->redirectRoute('orders.show', ['order' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('orders.edit', ['order' => $id], navigate: true);
    }

    public function delete($id): void
    {
        $this->resourceValue->orders()->findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The ORDER has been deleted.'
        );
    }

}
