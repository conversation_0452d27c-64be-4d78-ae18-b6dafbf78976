<?php

namespace App\Livewire\Components\InnerTable;

use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\WithPagination;
use Livewire\Attributes\Lazy;
use Livewire\Attributes\Computed;

#[Lazy]
class Products extends Component
{
    use WithPagination;
    
    public $resourceType;
    public $resourceNameSingular;
    public $resourceNamePlural;
    public $resourceValue;

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }
    
    public function render()
    {
        return view('livewire.components.inner-table.products');
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[Computed]
    public function products()
    {
        return $this->resourceValue->products()->orderBy('id', 'desc')->paginate(5, pageName: 'products-page');
    }
}
