<?php

namespace App\Livewire\Components\InnerTable;

use Flux\Flux;
use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\WithPagination;
use Livewire\Attributes\Lazy;
use App\Models\Order\OrderRow;
use Livewire\Attributes\Computed;
use App\Livewire\Forms\OrderRowForm;

#[Lazy]
class OrderRows extends Component
{
    use WithPagination;

    public ?OrderRowForm $form;

    public $resourceType;
    public $resourceNameSingular;
    public $resourceNamePlural;
    public $resourceValue;

    public $selectedGroup;

    public $itemsPerPage = 10;

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.inner-table.order-rows', [
            'groups' => $this->resourceValue->cartGroups,
        ]);
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[Computed]
    public function orderRows()
    {
        $query = $this->resourceValue->orderRows()
            ->with([
                'product' => function($query) {
                    $query->withTrashed()
                        ->with(['brand', 'collection', 'modules']);
                },
                'customProduct'
            ]);

        if ($this->selectedGroup) {
            $query->where('cart_group_id', $this->selectedGroup);
        }

        return $query->orderBy('sort')->paginate($this->itemsPerPage, pageName: 'order-rows-page');
    }

    public function view($id): void
    {
        $asset = OrderRow::findOrFail($id);

        $this->form->setOrderRow($asset);

        Flux::modal('view-order-row')->show();
    }

    public function edit($id): void
    {
        $asset = OrderRow::findOrFail($id);

        $this->form->setOrderRow($asset);

        Flux::modal('edit-order-row')->show();
    }

    public function update(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The ORDER ROW has been updated.'
        );
        
        Flux::modals()->close();
        $this->resetPage();
    }

    public function resetForm(): void
    {
        $this->form->reset();
        $this->resetValidation();
    }
}
