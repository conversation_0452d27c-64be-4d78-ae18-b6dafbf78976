<?php

namespace App\Livewire\Components\InnerTable;

use Flux\Flux;
use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\WithPagination;
use App\Models\DiscountGroup;
use Livewire\Attributes\Lazy;
use Livewire\Attributes\Computed;
use App\Livewire\Forms\DiscountGroupForm;

#[Lazy]
class DiscountGroups extends Component
{
    use WithPagination;

    public ?DiscountGroupForm $form;

    public $resourceType;
    public $resourceNameSingular;
    public $resourceNamePlural;
    public $resourceValue;

    public function mount($resourceType, $resourceValue): void
    {
        $this->resourceType = $resourceType;
        $this->resourceNameSingular = strtolower($resourceType);
        $this->resourceNamePlural = Str::plural(strtolower($resourceType));
        $this->resourceValue = $resourceValue;
    }

    public function render()
    {
        return view('livewire.components.inner-table.discount-groups');
    }

    public function placeholder()
    {
        return view('components.inner-table.lazy-placeholder');
    }

    #[Computed]
    public function discountGroups()
    {
        return $this->resourceValue->discountGroups()->orderBy('id', 'desc')->paginate(5, pageName: 'discount-groups-page');
    }

    public function view($id): void
    {
        $this->redirectRoute('discount-groups.show', ['discountGroup' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('discount-groups.edit', ['discountGroup' => $id], navigate: true);
    }

    public function delete($id): void
    {
        DiscountGroup::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The DISCOUNT GROUP has been deleted.'
        );
    }

    public function resetForm(): void
    {
        $this->form->reset();
    }
}
