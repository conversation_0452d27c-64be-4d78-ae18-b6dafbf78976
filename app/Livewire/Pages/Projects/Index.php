<?php

namespace App\Livewire\Pages\Projects;

use Flux\Flux;
use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Project\Project;
use Livewire\Attributes\Computed;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $itemsPerPage = 10;

    public $sortBy = 'id';
    public $sortDirection = 'desc';

    public function render()
    {
        return view('livewire.pages.projects.index');
    }

    #[Computed]
    public function projects()
    {
        return Project::query()
            ->where(function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('project_code', 'like', '%' . $this->search . '%')
                    ->orWhere('project_code_progressive', 'like', '%' . $this->search . '%')
                    ->orWhere('project_code_date', 'like', '%' . $this->search . '%')
                    ->orWhere('project_code_region', 'like', '%' . $this->search . '%')
                    ->orWhereHas('internalReferent', function ($query) {
                        $query->where('first_name', 'like', '%' . $this->search . '%')
                            ->orWhere('last_name', 'like', '%' . $this->search . '%');
                    })
                    ->orWhereHas('client', function ($query) {
                        $query->where('company', 'like', '%' . $this->search . '%');
                    })
                    ->orWhereHas('partner', function ($query) {
                        $query->where('company', 'like', '%' . $this->search . '%');
                    });
            })
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function view($id): void
    {
        $this->redirectRoute('projects.show', ['project' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('projects.edit', ['project' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Project::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The PROJECT has been deleted.'
        );
    }
}
