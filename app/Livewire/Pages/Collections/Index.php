<?php

namespace App\Livewire\Pages\Collections;

use Flux\Flux;
use Livewire\Component;
use App\Models\Collection;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $itemsPerPage = 10;

    public $sortBy = 'id';
    public $sortDirection = 'desc';
    
    public function render()
    {
        return view('livewire.pages.collections.index');
    }

    #[Computed]
    public function collections()
    {
        return Collection::query()
            ->where(function ($query) {
                $query->where('code', 'like', '%' . $this->search . '%')
                      ->orWhere('name', 'like', '%' . $this->search . '%');
            })
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function view($id): void
    {
        $this->redirectRoute('collections.show', ['collection' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('collections.edit', ['collection' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Collection::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The COLLECTION has been deleted.'
        );
    }
}
