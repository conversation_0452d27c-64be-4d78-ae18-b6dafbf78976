<?php

namespace App\Livewire\Pages\DiscountGroups;

use Flux\Flux;
use App\Models\Brand;
use Livewire\Component;
use App\Models\DiscountGroup;
use App\Livewire\Forms\DiscountGroupForm;

class Show extends Component
{
    public DiscountGroup $discountGroup;
    public DiscountGroupForm $form;

    public function mount()
    {
        $this->form->setDiscountGroup($this->discountGroup);
        $this->form->description = $this->discountGroup->description;
    }

    public function render()
    {
        return view('livewire.pages.discount-groups.show', [
            'brands' => Brand::all(),
        ]);
    }

    public function edit($id)
    {
        $this->redirectRoute('discount-groups.edit', ['discountGroup' => $id], navigate: true);
    }

    public function delete($id)
    {
        DiscountGroup::findOrFail($id)->delete();
        Flux::toast(
            variant: 'success',
            text: 'The DISCOUNT GROUP has been deleted.'
        );

        $this->redirectRoute('discount-groups.index', navigate: true);
    }
}
