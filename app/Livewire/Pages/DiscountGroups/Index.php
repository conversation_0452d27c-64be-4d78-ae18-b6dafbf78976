<?php

namespace App\Livewire\Pages\DiscountGroups;

use Flux\Flux;
use Livewire\Component;
use App\Models\DiscountGroup;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;

class Index extends Component
{
    use WithPagination;

    public $search = '';

    public $sortBy = 'id';
    public $sortDirection = 'desc';

    public function render()
    {
        return view('livewire.pages.discount-groups.index');
    }

    #[Computed]
    public function discountGroups()
    {
        return DiscountGroup::query()
            ->where('code', 'like', '%' . $this->search . '%')
            ->orWhere('description', 'like', '%' . $this->search . '%')
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate(10);
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function view($id): void
    {
        $this->redirectRoute('discount-groups.show', ['discountGroup' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('discount-groups.edit', ['discountGroup' => $id], navigate: true);
    }

    public function delete($id): void
    {
        DiscountGroup::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The DISCOUNT GROUP has been deleted.'
        );
    }
}
