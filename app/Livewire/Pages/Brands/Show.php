<?php

namespace App\Livewire\Pages\Brands;

use Flux\Flux;
use App\Models\Brand;
use Livewire\Component;
use App\Enums\BrandRating;
use App\Enums\BrandLeadTime;
use App\Enums\DeliveryTerms;
use Livewire\WithPagination;
use App\Enums\BrandPriceRange;
use App\Livewire\Forms\BrandForm;
use App\Enums\BrandPartnershipLevel;

class Show extends Component
{
    use WithPagination;
    
    public BrandForm $form;
    public Brand $brand;

    public function mount()
    {
        $this->form->setBrand($this->brand);
    }

    public function render()
    {
        return view('livewire.pages.brands.show', [
            'brandPriceRanges' => BrandPriceRange::cases(),
            'brandRatings' => BrandRating::cases(),
            'brandPartnershipLevels' => BrandPartnershipLevel::cases(),
            'brandLeadTimes' => BrandLeadTime::cases(),
            'deliveryTerms' => DeliveryTerms::cases(),
        ]);
    }

    public function edit($id): void
    {
        $this->redirectRoute('brands.edit', ['brand' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Brand::findOrFail($id)->delete();
        Flux::toast(
            variant: 'success',
            text: 'The BRAND has been deleted.'
        );

        $this->redirectRoute('brands.index', navigate: true);
    }
}
