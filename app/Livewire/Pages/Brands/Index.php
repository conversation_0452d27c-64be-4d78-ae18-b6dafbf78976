<?php

namespace App\Livewire\Pages\Brands;

use Flux\Flux;
use App\Models\Brand;
use Livewire\Component;
use Meilisearch\Client;
use App\Enums\BrandRating;
use App\Enums\BrandLeadTime;
use Livewire\WithPagination;
use App\Enums\BrandPriceRange;
use Livewire\Attributes\Computed;
use App\Enums\BrandPartnershipLevel;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $itemsPerPage = 10;

    public $sortBy = 'id';
    public $sortDirection = 'desc';

    public $selectedPriceRange = null;
    public $selectedRating = null;
    public $selectedPartnershipLevel = null;
    public $selectedLeadTime = null;

    // Tag filters
    public $selectedTypes = [];
    public $selectedMaterials = [];
    public $selectedDestinationRooms = [];
    public $selectedStyles = [];

    // Available options for tag filters
    public $availableTypes = [];
    public $availableMaterials = [];
    public $availableDestinationRooms = [];
    public $availableStyles = [];

    private Client $msClient; // MeiliSearch client

    public function __construct()
    {
        $this->msClient = new Client(config('scout.host'), config('scout.key'));
    }

    public function render()
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            $this->selectedPriceRange ? 'price_range = "' . $this->selectedPriceRange . '"' : null,
            $this->selectedRating ? 'rating = "' . $this->selectedRating . '"' : null,
            $this->selectedPartnershipLevel ? 'partnership_level = "' . $this->selectedPartnershipLevel . '"' : null,
            $this->selectedLeadTime ? 'lead_time = "' . $this->selectedLeadTime . '"' : null,
            count($this->selectedTypes) ? 'type IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedTypes)) . ']' : null,
            count($this->selectedMaterials) ? 'material IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedMaterials)) . ']' : null,
            count($this->selectedDestinationRooms) ? 'destination_room IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedDestinationRooms)) . ']' : null,
            count($this->selectedStyles) ? 'style IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedStyles)) . ']' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('brands')->search($this->search, [
            'facets' => ['price_range', 'rating', 'partnership_level', 'lead_time'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Extract facets from MeiliSearch response
        $facetsArray = $facets->getRaw();

        // Convert facets to enum instances
        $priceRanges = array_keys($facetsArray['facetDistribution']['price_range'] ?? []);
        $priceRanges = array_map(function ($value) {
            return BrandPriceRange::from($value);
        }, array_keys($facetsArray['facetDistribution']['price_range'] ?? []));

        // Convert facets to enum instances
        $ratings = array_keys($facetsArray['facetDistribution']['rating'] ?? []);
        $ratings = array_map(function ($value) {
            return BrandRating::from($value);
        }, array_keys($facetsArray['facetDistribution']['rating'] ?? []));

        // Convert facets to enum instances
        $partnershipLevels = array_keys($facetsArray['facetDistribution']['partnership_level'] ?? []);
        $partnershipLevels = array_map(function ($value) {
            return BrandPartnershipLevel::from($value);
        }, array_keys($facetsArray['facetDistribution']['partnership_level'] ?? []));

        // Convert facets to enum instances
        $leadTimes = array_keys($facetsArray['facetDistribution']['lead_time'] ?? []);
        $leadTimes = array_map(function ($value) {
            return BrandLeadTime::from($value);
        }, array_keys($facetsArray['facetDistribution']['lead_time'] ?? []));

        return view('livewire.pages.brands.index', [
            'priceRanges' => $priceRanges,
            'ratings' => $ratings,
            'partnershipLevels' => $partnershipLevels,
            'leadTimes' => $leadTimes,
        ]);
    }

    #[Computed]
    public function brands()
    {
        $query = Brand::search($this->search ?: '*')
            ->when($this->selectedPriceRange, function ($query) {
                $query->where('price_range', $this->selectedPriceRange);
            })
            ->when($this->selectedRating, function ($query) {
                $query->where('rating', $this->selectedRating);
            })
            ->when($this->selectedPartnershipLevel, function ($query) {
                $query->where('partnership_level', $this->selectedPartnershipLevel);
            })
            ->when($this->selectedLeadTime, function ($query) {
                $query->where('lead_time', $this->selectedLeadTime);
            })
            ->when(count($this->selectedTypes), fn($q) => $q->whereIn('type', $this->selectedTypes))
            ->when(count($this->selectedMaterials), fn($q) => $q->whereIn('material', $this->selectedMaterials))
            ->when(count($this->selectedDestinationRooms), fn($q) => $q->whereIn('destination_room', $this->selectedDestinationRooms))
            ->when(count($this->selectedStyles), fn($q) => $q->whereIn('style', $this->selectedStyles));

        return $query
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function updatedSelectedPriceRange()
    {
        $this->resetPage();
    }

    public function updatedSelectedRating()
    {
        $this->resetPage();
    }

    public function updatedSelectedPartnershipLevel()
    {
        $this->resetPage();
    }

    public function updatedSelectedLeadTime()
    {
        $this->resetPage();
    }

    public function updatedSelectedTypes()
    {
        $this->resetPage();
    }

    public function updatedSelectedMaterials()
    {
        $this->resetPage();
    }

    public function updatedSelectedDestinationRooms()
    {
        $this->resetPage();
    }

    public function updatedSelectedStyles()
    {
        $this->resetPage();
    }

    public function view($id): void
    {
        $this->redirectRoute('brands.show', ['brand' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('brands.edit', ['brand' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Brand::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The BRAND has been deleted.'
        );
    }

    private function getPriceRanges(): array
    {
        return BrandPriceRange::cases();
    }

    private function getRatings(): array
    {
        return BrandRating::cases();
    }

    private function getPartnershipLevels(): array
    {
        return BrandPartnershipLevel::cases();
    }

    private function getLeadTimes(): array
    {
        return BrandLeadTime::cases();
    }

    public function fetchAvailableTypes(): void
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            $this->selectedPriceRange ? 'price_range = "' . $this->selectedPriceRange . '"' : null,
            $this->selectedRating ? 'rating = "' . $this->selectedRating . '"' : null,
            $this->selectedPartnershipLevel ? 'partnership_level = "' . $this->selectedPartnershipLevel . '"' : null,
            $this->selectedLeadTime ? 'lead_time = "' . $this->selectedLeadTime . '"' : null,
            count($this->selectedMaterials) ? 'material IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedMaterials)) . ']' : null,
            count($this->selectedDestinationRooms) ? 'destination_room IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedDestinationRooms)) . ']' : null,
            count($this->selectedStyles) ? 'style IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedStyles)) . ']' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('brands')->search($this->search ?: '*', [
            'facets' => ['type'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Get available types from facets
        $this->availableTypes = array_keys($facets->getRaw()['facetDistribution']['type'] ?? []);
    }

    public function fetchAvailableMaterials(): void
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            $this->selectedPriceRange ? 'price_range = "' . $this->selectedPriceRange . '"' : null,
            $this->selectedRating ? 'rating = "' . $this->selectedRating . '"' : null,
            $this->selectedPartnershipLevel ? 'partnership_level = "' . $this->selectedPartnershipLevel . '"' : null,
            $this->selectedLeadTime ? 'lead_time = "' . $this->selectedLeadTime . '"' : null,
            count($this->selectedTypes) ? 'type IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedTypes)) . ']' : null,
            count($this->selectedDestinationRooms) ? 'destination_room IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedDestinationRooms)) . ']' : null,
            count($this->selectedStyles) ? 'style IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedStyles)) . ']' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('brands')->search($this->search ?: '*', [
            'facets' => ['material'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Get available materials from facets
        $this->availableMaterials = array_keys($facets->getRaw()['facetDistribution']['material'] ?? []);
    }

    public function fetchAvailableDestinationRooms(): void
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            $this->selectedPriceRange ? 'price_range = "' . $this->selectedPriceRange . '"' : null,
            $this->selectedRating ? 'rating = "' . $this->selectedRating . '"' : null,
            $this->selectedPartnershipLevel ? 'partnership_level = "' . $this->selectedPartnershipLevel . '"' : null,
            $this->selectedLeadTime ? 'lead_time = "' . $this->selectedLeadTime . '"' : null,
            count($this->selectedTypes) ? 'type IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedTypes)) . ']' : null,
            count($this->selectedMaterials) ? 'material IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedMaterials)) . ']' : null,
            count($this->selectedStyles) ? 'style IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedStyles)) . ']' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('brands')->search($this->search ?: '*', [
            'facets' => ['destination_room'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Get available destination rooms from facets
        $this->availableDestinationRooms = array_keys($facets->getRaw()['facetDistribution']['destination_room'] ?? []);
    }

    public function fetchAvailableStyles(): void
    {
        // Convert selected filters to MeiliSearch filters
        $filters = array_filter([
            $this->selectedPriceRange ? 'price_range = "' . $this->selectedPriceRange . '"' : null,
            $this->selectedRating ? 'rating = "' . $this->selectedRating . '"' : null,
            $this->selectedPartnershipLevel ? 'partnership_level = "' . $this->selectedPartnershipLevel . '"' : null,
            $this->selectedLeadTime ? 'lead_time = "' . $this->selectedLeadTime . '"' : null,
            count($this->selectedTypes) ? 'type IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedTypes)) . ']' : null,
            count($this->selectedMaterials) ? 'material IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedMaterials)) . ']' : null,
            count($this->selectedDestinationRooms) ? 'destination_room IN [' . implode(',', array_map(fn($v) => '"' . $v . '"', $this->selectedDestinationRooms)) . ']' : null,
        ]);

        // Get facets from MeiliSearch
        $facets = $this->msClient->index('brands')->search($this->search ?: '*', [
            'facets' => ['style'],
            'filter' => implode(' AND ', $filters),
        ]);

        // Get available styles from facets
        $this->availableStyles = array_keys($facets->getRaw()['facetDistribution']['style'] ?? []);
    }

    public function areFiltersActive(): bool
    {
        return !empty($this->search) ||
               !empty($this->selectedPriceRange) ||
               !empty($this->selectedRating) ||
               !empty($this->selectedPartnershipLevel) ||
               !empty($this->selectedLeadTime) ||
               !empty($this->selectedTypes) ||
               !empty($this->selectedMaterials) ||
               !empty($this->selectedDestinationRooms) ||
               !empty($this->selectedStyles);
    }

    public function clearSearchAndFilters(): void
    {
        $this->search = '';
        $this->selectedPriceRange = null;
        $this->selectedRating = null;
        $this->selectedPartnershipLevel = null;
        $this->selectedLeadTime = null;
        $this->selectedTypes = [];
        $this->selectedMaterials = [];
        $this->selectedDestinationRooms = [];
        $this->selectedStyles = [];
        $this->resetPage();
    }
}
