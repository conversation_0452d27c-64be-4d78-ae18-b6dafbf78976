<?php

namespace App\Livewire\Pages\Roles;

use App\Enums\ModelTypes;
use App\Livewire\Forms\RoleForm;
use Flux\Flux;
use Livewire\Component;

class Create extends Component
{
    public RoleForm $form;

    public function mount(): void
    {
        $this->form->permissions = ModelTypes::mapPermissions();
    }
    public function render()
    {
        return view('livewire.pages.roles.create',[
            'resources' => ModelTypes::cases(),
        ]);
    }

    public function save(): void
    {
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The ROLE has been created.'
        );

        $this->redirectRoute('roles.index', navigate: true);
    }
}
