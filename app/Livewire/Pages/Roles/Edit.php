<?php

namespace App\Livewire\Pages\Roles;

use App\Enums\ModelTypes;
use App\Livewire\Forms\RoleForm;
use Flux\Flux;
use Livewire\Component;
use Spatie\Permission\Models\Role;

class Edit extends Component
{
    public Role $role;
    public RoleForm $form;

    public function mount(): void
    {
        $this->form->permissions = ModelTypes::mapPermissions();
        $this->form->setRole($this->role);
    }

    public function render()
    {
        return view('livewire.pages.roles.edit', [
            'resources' => ModelTypes::cases(),
        ]);
    }

    public function save(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The ROLE has been updated.'
        );

        $this->redirectRoute('roles.index', navigate: true);
    }
}
