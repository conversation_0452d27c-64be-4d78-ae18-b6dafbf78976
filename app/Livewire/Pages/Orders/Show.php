<?php

namespace App\Livewire\Pages\Orders;

use Flux\Flux;
use App\Models\User;
use App\Models\Client;
use App\Enums\VatTypes;
use App\Models\Address;
use App\Models\Partner;
use Livewire\Component;
use App\Enums\AddressTypes;
use App\Models\Order\Order;
use App\Models\PaymentTerm;
use Livewire\Attributes\On;
use App\Enums\OrderStatuses;
use App\Exports\OrderExport;
use App\Exports\OrderRowsExport;
use App\Livewire\Forms\OrderForm;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use App\Actions\Orders\Statuses\Submit;
use App\Actions\Orders\Statuses\ReOpen;
use App\Actions\Orders\Statuses\Approve;
use App\Actions\Orders\Statuses\Reject;
use Illuminate\Support\Facades\Storage;
use App\Actions\Orders\Exports\DownloadPDF;
use PrinsFrank\Standards\Country\CountryAlpha3;

class Show extends Component
{
    public Order $order;
    public OrderForm $form;

    public object $callout;

    public function mount(Order $order)
    {
        $this->callout = (object) [
            'show' => false,
            'variant' => 'info',
            'icon' => 'information-circle',
            'heading' => 'Here a message.'
        ];

        $this->form->setOrder($order);
    }

    public function render()
    {
        $client = Client::find($this->form->client_id);

        // Set the callout message if order contains soft deleted products
        if ($this->order->orderRows()->whereHas('product', function ($query) {
            $query->onlyTrashed();
        })->count() > 0 && $this->order->status->value === OrderStatuses::Open->value) {
            $this->callout = (object) [
                'show' => true,
                'variant' => 'warning',
                'icon' => 'exclamation-circle',
                'heading' => 'This order contains OUT OF STOCK products. Please replace them!'
            ];
        }

        return view('livewire.pages.orders.show', [
            'internalReferents' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'areaManagers' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'clients' => Client::orderBy('id', 'desc')->get(),
            'partners' => $this->form->client_id 
                ? Partner::where('id', $client?->partner_id)->get()
                : [],
            'invoicingAddresses' => $this->form->client_id 
                ? Address::where('addressable_id', $this->form->client_id)
                    ->where('addressable_type', Client::class)
                    ->where('type', AddressTypes::Invoicing->value)
                    ->orderBy('id', 'desc')
                    ->get()
                : [],
            'shippingAddresses' => $this->form->client_id 
                ? Address::where('addressable_id', $this->form->client_id)
                    ->where('addressable_type', Client::class)
                    ->where('type', AddressTypes::Shipping->value)
                    ->orderBy('id', 'desc')
                    ->get()
                : [],
            'paymentTerms' => PaymentTerm::orderBy('id', 'desc')->get(),
            'vatTypes' => VatTypes::cases(),
            'projects' => $this->form->client_id 
                ? $client?->projects
                : [],
            'addressTypes' => AddressTypes::cases(),
            'countries' => CountryAlpha3::cases(),
        ]);
    }

    public function edit($id): void
    {
        $this->redirectRoute('orders.edit', ['order' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Order::findOrFail($id)->delete();
        
        Flux::toast(
            variant: 'success',
            text: 'The ORDER has been deleted.'
        );

        $this->redirectRoute('orders.index', navigate: true);
    }

    public function downloadConfirmation()
    {
        if (!Storage::disk(config('filesystems.private'))->exists($this->form->confirmation_file ?? ' ')) {

            Flux::toast(
                variant: 'danger',
                text: 'The FILE does not exist.'
            );

            return false;
        }

        $url = Storage::disk(config('filesystems.private'))
            ->temporaryUrl($this->form->confirmation_file, now()->addMinutes(5));

        return response()->streamDownload(function () use ($url) {
            echo file_get_contents($url);
        }, basename($this->form->confirmation_file));
    }

    public function openCreator($id): void
    {
        $this->redirectRoute('orders.creator', ['order' => $id], navigate: true);
    }

    public function submit()
    {
        $result = Submit::run($this->order);

        if ($result['success']) {
            $this->order->refresh();
        }

        Flux::toast(
            variant: $result['success'] ? 'success' : 'danger',
            text: $result['message']
        );
    }

    public function reOpen()
    {
        $result = ReOpen::run($this->order);

        if ($result['success']) {
            $this->order->refresh();
        }

        Flux::toast(
            variant: $result['success'] ? 'success' : 'danger',
            text: $result['message']
        );
    }

    public function approve()
    {
        $result = Approve::run($this->order, Auth::id());

        Flux::toast(
            variant: $result['success'] ? 'success' : 'danger',
            text: $result['message']
        );
    }

    public function reject()
    {
        $result = Reject::run($this->order);

        Flux::toast(
            variant: $result['success'] ? 'success' : 'danger',
            text: $result['message']
        );
    }

    public function showInvoicingAddress()
    {
        if (!$this->order->invoicingAddress) {
            Flux::toast(
                variant: 'danger',
                text: 'The ORDER has no invoicing address.'
            );

            return false;
        }

        Flux::modal('view-invoicing-address')->show();
    }

    public function showShippingAddress()
    {
        if (!$this->order->shippingAddress) {
            Flux::toast(
                variant: 'danger',
                text: 'The ORDER has no shipping address.'
            );

            return false;
        }

        Flux::modal('view-shipping-address')->show();
    }

    #[On('order-download-pdf')]
    public function downloadOrderPdf()
    {
        return DownloadPDF::run($this->order);
    }

    #[On('order-download-excel')]
    public function downloadOrderExcel()
    {
        $fileName = str_replace('/', '-', $this->order->code) . '.xlsx';
        
        return Excel::download(new OrderExport($this->order), $fileName);
    }

    #[On('order-rows-download-excel')]
    public function downloadOrderRowsExcel()
    {
        $fileName = str_replace('/', '-', $this->order->code) . '.xlsx';
        
        return Excel::download(new OrderRowsExport($this->order), $fileName);
    }
}
