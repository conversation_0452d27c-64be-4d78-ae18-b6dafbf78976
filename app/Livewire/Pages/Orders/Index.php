<?php

namespace App\Livewire\Pages\Orders;

use Flux\Flux;
use Livewire\Component;
use App\Models\Order\Order;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $itemsPerPage = 10;

    public $sortBy = 'id';
    public $sortDirection = 'desc';
    
    public function render()
    {
        return view('livewire.pages.orders.index');
    }

    #[Computed]
    public function orders()
    {
        return Order::query()
            ->where(function ($query) {
                $query->where('code', 'like', '%' . $this->search . '%')
                      ->orWhere('order_code', 'like', '%' . $this->search . '%')
                      ->orWhere('order_code_progressive', 'like', '%' . $this->search . '%')
                      ->orWhere('order_code_date', 'like', '%' . $this->search . '%')
                      ->orWhere('order_code_type', 'like', '%' . $this->search . '%')
                      ->orWhere('order_code_region', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%')
                      ->orWhereHas('internalReferent', function ($query) {
                        $query->where('first_name', 'like', '%' . $this->search . '%')
                              ->orWhere('last_name', 'like', '%' . $this->search . '%');
                      })
                      ->orWhereHas('client', function ($query) {
                        $query->where('company', 'like', '%' . $this->search . '%');
                      })
                      ->orWhereHas('partner', function ($query) {
                        $query->where('company', 'like', '%' . $this->search . '%');
                      });
            })
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function view($id): void
    {
        $this->redirectRoute('orders.show', ['order' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('orders.edit', ['order' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Order::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The ORDER has been deleted.'
        );
    }
}
