<?php

namespace App\Livewire\Pages\Clients;

use Flux\Flux;
use App\Models\Client;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $itemsPerPage = 10;

    public $sortBy = 'id';
    public $sortDirection = 'desc';

    public function render()
    {
        return view('livewire.pages.clients.index');
    }

    #[Computed]
    public function clients()
    {
        return Client::query()
            ->where(function ($query) {
                $query->where('company', 'like', '%' . $this->search . '%')
                      ->orWhere('type', 'like', '%' . $this->search . '%')
                      ->orWhere('commercial_category', 'like', '%' . $this->search . '%')
                      ->orWhere('priority', 'like', '%' . $this->search . '%')
                      ->orWhereHas('internalReferent', function ($query) {
                        $query->where('first_name', 'like', '%' . $this->search . '%')
                              ->orWhere('last_name', 'like', '%' . $this->search . '%');
                      })
                      ->orWhereHas('partner', function ($query) {
                        $query->where('company', 'like', '%' . $this->search . '%');
                      });
            })
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function view($id): void
    {
        $this->redirectRoute('clients.show', ['client' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('clients.edit', ['client' => $id], navigate: true);
    }

    public function delete($id): void
    {
        $client = Client::findOrFail($id);

        // Check if the client is associated to any orders
        if ($client->orders()->count() > 0) {
            Flux::toast(
                variant: 'danger',
                text: 'You cannot delete this CLIENT because it is associated to one or more orders.'
            );
        }
        else {
            $client->delete();

            Flux::toast(
                variant: 'success',
                text: 'The CLIENT has been deleted.'
            );
        }
    }
}
