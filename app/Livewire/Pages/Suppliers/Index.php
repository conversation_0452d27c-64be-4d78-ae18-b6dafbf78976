<?php

namespace App\Livewire\Pages\Suppliers;

use Flux\Flux;
use App\Models\Supplier;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $itemsPerPage = 10;

    public $sortBy = 'id';
    public $sortDirection = 'desc';

    public function render()
    {
        return view('livewire.pages.suppliers.index');
    }

    #[Computed]
    public function suppliers()
    {
        return Supplier::query()
            ->where('name', 'like', '%' . $this->search . '%')
            ->orWhere('code', 'like', '%' . $this->search . '%')
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function view($id): void
    {
        $this->redirectRoute('suppliers.show', ['supplier' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('suppliers.edit', ['supplier' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Supplier::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The SUPPLIER has been deleted.'
        );
    }
}