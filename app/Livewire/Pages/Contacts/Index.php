<?php

namespace App\Livewire\Pages\Contacts;

use Flux\Flux;
use App\Models\Contact;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $itemsPerPage = 10;

    public $sortBy = 'id';
    public $sortDirection = 'desc';

    public function render()
    {
        return view('livewire.pages.contacts.index');
    }

    #[Computed]
    public function contacts()
    {
        return Contact::query()
            ->where(function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('departments', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%')
                      ->orWhere('phone', 'like', '%' . $this->search . '%');
            })
            ->tap(fn ($query) => $this->sortBy ? $query->orderBy($this->sortBy, $this->sortDirection) : $query)
            ->paginate($this->itemsPerPage);
    }

    public function sort($column) {
        if ($this->sortBy === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->sortBy = 'id';
        $this->sortDirection = 'desc';
    }

    public function view($id): void
    {
        $this->redirectRoute('contacts.show', ['contact' => $id], navigate: true);
    }

    public function edit($id): void
    {
        $this->redirectRoute('contacts.edit', ['contact' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Contact::findOrFail($id)->delete();

        Flux::toast(
            variant: 'success',
            text: 'The CONTACT has been deleted.'
        );
    }
}
