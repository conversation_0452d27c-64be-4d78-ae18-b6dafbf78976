<?php

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class UserRoleScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        if (auth()->user()?->isAdmin()) {
            return;
        }

        $userId = auth()->id();
        $builder->whereHas('users', function ($query) use ($userId) {
            $query->whereKey($userId);
        });
    }
}
