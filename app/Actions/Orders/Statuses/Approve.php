<?php

namespace App\Actions\Orders\Statuses;

use App\Models\Order\Order;
use App\Enums\OrderStatuses;
use App\Enums\ExportSources;
use App\Jobs\Export\ExportOrderFilesJob;
use Illuminate\Support\Facades\DB;

class Approve
{
    public static function run(Order $order, ?int $userId = null): array
    {
        DB::beginTransaction();

        try {
            // Check if the order is in SUBMITTED status
            if ($order->status !== OrderStatuses::Submitted) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'The ORDER can only be approved if it is in SUBMITTED status.'
                ];
            }

            // Update the order status to APPROVED
            $order->update([
                'status' => OrderStatuses::Approved->value
            ]);

            DB::commit();

            // Dispatch job to create xlsx file for invoicing address, shipping address and order (to be imported in the ERP)
            // Note: Dispatched AFTER commit to ensure data consistency
            if ($userId) {
                ExportOrderFilesJob::dispatch(
                    $order,
                    ExportSources::OrderSubmit,
                    $userId
                )->onQueue('default');
            }

            return [
                'success' => true,
                'message' => 'The ORDER has been approved.'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'An error occurred while approving the order.'
            ];
        }
    }
}
