<?php

namespace App\Actions\Orders\Exports;

use App\Models\Order\Order;
use Barryvdh\DomPDF\Facade\Pdf;

class DownloadPDF
{
    public static function run(Order $order)
    {
        $order = $order->load([
            'client', 
            'client.discountGroups',
            'paymentTerm',
            'paymentTerm.items',
            'addons',
            'cartGroups' => function($query) {
                $query->orderBy('sort');
            },
            'cartGroups.orderRows' => function($query) {
                $query->orderBy('sort');
            },
            'cartGroups.orderRows.product',
            'cartGroups.orderRows.product.brand'
        ]);
        
        $sortedCartGroups = $order->cartGroups;
        
        $orderData = [
            'order' => $order,
            'totalAmount' => eu_currency($order->totalAmount),
            'addonsAmount' => eu_currency($order->addonsAmount),
            'vatTotal' => eu_currency($order->getVatTotalAttribute($withAddons = true)),
            'grossTotal' => eu_currency($order->getTotalAmountAttribute($withAddons = true, $withVat = true)),
            'payments' => $order->payments,
            'sortedCartGroups' => $sortedCartGroups
        ];
        
        $pdf = Pdf::loadView('exports.order', $orderData);
        $pdf->setOption([
            'isRemoteEnabled' => true,
        ]);

        $fileName = str_replace('/', '-', $order->code) . '.pdf';

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, $fileName);
    }
}
