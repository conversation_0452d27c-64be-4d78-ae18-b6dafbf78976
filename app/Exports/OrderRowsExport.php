<?php

namespace App\Exports;

use App\Models\Order\Order;
use App\Models\Order\OrderRow;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;

class OrderRowsExport implements FromQuery, WithHeadings, WithMapping
{
    protected $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }
    
    public function query()
    {
        // Get cart group IDs associated with this order
        $cartGroupIds = $this->order->cartGroups()->pluck('id')->toArray();
        
        // Filter order rows by these cart group IDs
        return OrderRow::query()->whereIn('cart_group_id', $cartGroupIds)->orderBy('sort');
    }

    public function headings(): array
    {
        return [
            'ADHOC_SKU',
            'QUANTITY',
            'PRICE',
            'DISCOUNT',
        ];
    }

    public function map($row): array
    {
        return [
            $row->options ? $row->getModularSku() : $row->product?->adhoc_sku ?? $row->customProduct?->sku ?? '',
            $row->quantity,
            isset($row->selling_price_override) ? $row->selling_price_override : ($row->selling_price ?? $row->product->selling_price ?? 0),
            isset($row->discount_override) ? "-" . $row->discount_override : "-" . ($row->discount ?? $row->getDiscountForClient()),
        ];
    }
}
