<?php

namespace Database\Seeders;

use App\Enums\AddressTypes;
use App\Models\Brand;
use App\Models\Client;
use App\Models\Address;
use App\Models\Contact;
use Illuminate\Database\Seeder;

class ClientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Client::factory(25)->create();

        Client::all()->each(function ($client) {
            // Attach 3 random contacts to the client
            $contacts = Contact::inRandomOrder()->take(3)->get();
            $client->contacts()->attach($contacts->pluck('id')->toArray());

            // Attach 3 random brands to the client
            if ($client->partner) {
                $brands = $client->partner->brands()->inRandomOrder()->take(3)->get();
            } else {
                $brands = Brand::inRandomOrder()->take(3)->get();
            }
            $client->brands()->attach($brands->pluck('id')->toArray());

            // Attach 3 random addresses to the client
            $addresses = Address::factory(3)->create([
                'addressable_id' => $client->id,
                'addressable_type' => Client::class,
            ]);
            $client->addresses()->saveMany($addresses);

            // Select only addresses of type Shipping and set their parent_id to a random Invoicing address
            $shippingAddresses = $addresses->where('type', AddressTypes::Shipping);
            $invoicingAddresses = $client->addresses()->where('type', AddressTypes::Invoicing)->get();

            foreach ($shippingAddresses as $shippingAddress) {
                if ($invoicingAddresses->isNotEmpty()) {
                    $randomInvoicingAddress = $invoicingAddresses->random();
                    $shippingAddress->parent_id = $randomInvoicingAddress->id;
                    $shippingAddress->save();
                }
            }

            // Attach 3 random discount groups to the client
            $discountGroups = \App\Models\DiscountGroup::inRandomOrder()->take(3)->pluck('id')->toArray();
            foreach ($discountGroups as $discountGroupId) {
                $discount = \App\Models\DiscountGroup::find($discountGroupId)->discount;
                $client->discountGroups()->attach($discountGroupId, [
                    'discount' => round(mt_rand(0, $discount * 100) / 100, 2),
                ]);
            }

            // Attach a random payment term to the client
            $paymentTerm = \App\Models\PaymentTerm::inRandomOrder()->first();
            $client->paymentTerm()->associate($paymentTerm);
            $client->save();
        });
    }
}