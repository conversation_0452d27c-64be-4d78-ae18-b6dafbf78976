<?php

namespace Database\Seeders;

use App\Models\Brand;
use App\Models\Address;
use App\Models\Contact;
use App\Models\Partner;
use App\Enums\AddressTypes;
use Illuminate\Database\Seeder;

class PartnerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Partner::factory(25)->create();

        Partner::all()->each(function ($partner) {
            // Attach 3 random contacts to the partner
            $contacts = Contact::inRandomOrder()->take(3)->get();
            $partner->contacts()->attach($contacts->pluck('id')->toArray());

            // Attach 3 random brands to the partner
            $brands = Brand::inRandomOrder()->take(3)->get();
            $partner->brands()->attach($brands->pluck('id')->toArray());

            // Attach 3 random addresses to the partner
            $addresses = Address::factory(3)->create([
                'addressable_id' => $partner->id,
                'addressable_type' => Partner::class,
            ]);
            $partner->addresses()->saveMany($addresses);

            // Select only addresses of type Shipping and set their parent_id to a random Invoicing address
            $shippingAddresses = $addresses->where('type', AddressTypes::Shipping);
            $invoicingAddresses = $partner->addresses()->where('type', AddressTypes::Invoicing)->get();

            foreach ($shippingAddresses as $shippingAddress) {
                if ($invoicingAddresses->isNotEmpty()) {
                    $randomInvoicingAddress = $invoicingAddresses->random();
                    $shippingAddress->parent_id = $randomInvoicingAddress->id;
                    $shippingAddress->save();
                }
            }

            // Attach 3 random discount groups to the partner
            $discountGroups = \App\Models\DiscountGroup::inRandomOrder()->take(3)->pluck('id')->toArray();
            foreach ($discountGroups as $discountGroupId) {
                $discount = \App\Models\DiscountGroup::find($discountGroupId)->discount;
                $partner->discountGroups()->attach($discountGroupId, [
                    'discount' => round(mt_rand(0, $discount * 100) / 100, 2),
                ]);
            }

            // Attach a random payment term to the partner
            $paymentTerm = \App\Models\PaymentTerm::inRandomOrder()->first();
            $partner->paymentTerm()->associate($paymentTerm);
            $partner->save();
        });
    }
}