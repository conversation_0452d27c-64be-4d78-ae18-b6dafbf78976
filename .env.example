APP_NAME=ErgoUp
APP_ENV=local
APP_KEY=
APP_DEBUG=false
APP_TIMEZONE=Europe/Rome
APP_URL=

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

APP_FEED_SIMPLE_PRODUCTS=
APP_FEED_VARIANT_PRODUCTS=
APP_FEED_MODULAR_PRODUCTS=
APP_FEED_MODULAR_OPTIONS=

APP_FEED_PRODUCTS_TO_BE_DELETED=

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

NIGHTWATCH_TOKEN=
NIGHTWATCH_REQUEST_SAMPLE_RATE=

DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=bergomi
DB_USERNAME=lpudia
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

FILESYSTEM_DISK=local
FILESYSTEM_PRIVATE=local
FILESYSTEM_PUBLIC=public
FILESYSTEM_EXCHANGE=local

SCOUT_DRIVER=meilisearch
MEILISEARCH_HOST=
MEILISEARCH_KEY=

BROADCAST_CONNECTION=log
QUEUE_CONNECTION=redis

CACHE_STORE=redis
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=predis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

VITE_APP_NAME="${APP_NAME}"
